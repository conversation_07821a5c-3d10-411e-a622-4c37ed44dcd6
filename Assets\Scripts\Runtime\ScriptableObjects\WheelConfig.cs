using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;
using F8Framework.Core;

namespace QHLC
{
    namespace ScriptableObjects
    {
    /// <summary>
    /// 转盘配置
    /// </summary>
    [CreateAssetMenu(fileName = "WheelConfig", menuName = "QHLC/WheelConfig", order = 2)]
    public class WheelConfig : ScriptableObject
    {
        [Header("转盘旋转设置")]
        [Tooltip("最小旋转速度")]
        public float minSpinSpeed = 360f; // 每秒一圈

        [Tooltip("最大旋转速度")]
        public float maxSpinSpeed = 720f; // 每秒两圈

        [Tooltip("加速时间（秒）")]
        public float accelerationDuration = 0.8f; // 增加加速时间，让加速过程更明显

        [Tooltip("匀速旋转时间（秒）")]
        public float spinDuration = 1.2f; // 适当减少匀速时间

        [Tooltip("减速时间（秒）")]
        public float slowDownDuration = 1.6f; // 增加减速时间，让减速过程更自然

        [Tooltip("初始角度")]
        public float initialAngle = 0f;

        [Tooltip("额外旋转圈数")]
        public int additionalRotations = 4; // 增加旋转圈数，让动画更有趣

        [Header("转盘闪烁设置")]
        [Tooltip("闪烁持续时间（秒）")]
        public float flashDuration = 0.6f; // 增加闪烁持续时间

        [Tooltip("闪烁次数")]
        public int flashCount = 4; // 增加闪烁次数

        [Header("转盘判定设置")]
        [Tooltip("判定角度范围")]
        public float judgmentAngle = 36f;

        [Serializable]
        public class ResultAngleMapping
        {
            public WheelResult result;
            public float angle;
        }

        [Header("转盘结果角度映射")]

        [Tooltip("转盘结果角度映射")]
        public ResultAngleMapping[] resultAngleMappings = new ResultAngleMapping[]
        {
            new ResultAngleMapping { result = WheelResult.Stop, angle = -72f },
            new ResultAngleMapping { result = WheelResult.Move1, angle = 0f },
            new ResultAngleMapping { result = WheelResult.Move2, angle = 72f },
            new ResultAngleMapping { result = WheelResult.Move3, angle = 144f },
            new ResultAngleMapping { result = WheelResult.Move4, angle = 216f }
        };

        [Serializable]
        public class ResultProbability
        {
            public WheelResult result;
            [Range(0f, 1f)]
            public float probability;
        }

        [Header("转盘结果概率设置")]

        [Tooltip("转盘结果概率设置")]
        public ResultProbability[] resultProbabilities = new ResultProbability[]
        {
            new ResultProbability { result = WheelResult.Stop, probability = 0.05f },  // 降低Stop的概率
            new ResultProbability { result = WheelResult.Move1, probability = 0.25f },  // 增加Move1的概率
            new ResultProbability { result = WheelResult.Move2, probability = 0.25f },  // 增加Move2的概率
            new ResultProbability { result = WheelResult.Move3, probability = 0.25f },  // 增加Move3的概率
            new ResultProbability { result = WheelResult.Move4, probability = 0.20f }   // 增加Move4的概率
        };

        /// <summary>
        /// 获取结果对应的角度
        /// </summary>
        public float GetResultAngle(WheelResult result)
        {
            foreach (var mapping in resultAngleMappings)
            {
                if (mapping.result == result)
                {
                    return mapping.angle;
                }
            }

            // 默认返回Stop的角度
            LogF8.LogWarning($"未找到结果{result}的角度映射，返回默认值");
            return -72f;
        }

        /// <summary>
        /// 获取角度对应的结果
        /// </summary>
        public WheelResult GetResultFromAngle(float angle)
        {
            // 将角度标准化到0-360范围
            angle = NormalizeAngle(angle);

            // 计算每个结果的角度范围
            foreach (var mapping in resultAngleMappings)
            {
                float resultAngle = NormalizeAngle(mapping.angle);
                float minAngle = NormalizeAngle(resultAngle - judgmentAngle);
                float maxAngle = NormalizeAngle(resultAngle + judgmentAngle);

                if (IsAngleInRange(angle, minAngle, maxAngle))
                {
                    return mapping.result;
                }
            }

            // 默认返回Stop
            LogF8.LogWarning($"未找到角度{angle}对应的结果，返回默认值");
            return WheelResult.Stop;
        }

        /// <summary>
        /// 获取随机结果（基于概率）
        /// </summary>
        /// <param name="excludeStop">是否排除Stop结果</param>
        public WheelResult GetRandomResult(bool excludeStop = false)
        {
            LogF8.LogError($"WheelConfig.GetRandomResult: 开始生成随机结果，排除Stop: {excludeStop}");

            // 检查概率设置是否有效
            if (resultProbabilities == null || resultProbabilities.Length == 0)
            {
                LogF8.LogError("WheelConfig.GetRandomResult: 概率设置无效，返回默认值 Move1");
                return WheelResult.Move1; // 改为返回Move1而非Stop
            }

            // 过滤掉概率为0的结果，如果excludeStop为true则同时排除Stop结果
            var validProbabilities = new System.Collections.Generic.List<ResultProbability>();
            foreach (var prob in resultProbabilities)
            {
                if (prob.probability > 0 && (!excludeStop || prob.result != WheelResult.Stop))
                {
                    validProbabilities.Add(prob);
                    LogF8.LogError($"WheelConfig.GetRandomResult: 有效结果 {prob.result} 的概率为 {prob.probability}");
                }
            }

            // 如果没有有效的概率设置，返回默认值
            if (validProbabilities.Count == 0)
            {
                LogF8.LogError("WheelConfig.GetRandomResult: 没有有效的概率设置，返回默认值 Move1");
                return WheelResult.Move1; // 改为返回Move1而非Stop
            }

            float totalProbability = 0f;
            foreach (var prob in validProbabilities)
            {
                totalProbability += prob.probability;
            }
            LogF8.LogError($"WheelConfig.GetRandomResult: 总概率为 {totalProbability}");

            float randomValue = UnityEngine.Random.Range(0f, totalProbability);
            LogF8.LogError($"WheelConfig.GetRandomResult: 生成的随机值为 {randomValue}");

            float currentProbability = 0f;

            foreach (var prob in validProbabilities)
            {
                currentProbability += prob.probability;
                LogF8.LogError($"WheelConfig.GetRandomResult: 当前累计概率为 {currentProbability}");

                if (randomValue <= currentProbability)
                {
                    LogF8.LogError($"WheelConfig.GetRandomResult: 生成随机结果: {prob.result} ({(int)prob.result})");
                    return prob.result;
                }
            }

            LogF8.LogError("WheelConfig.GetRandomResult: 生成随机结果失败，返回默认值 Move1");
            // 默认返回Move1而非Stop
            return WheelResult.Move1;
        }

        /// <summary>
        /// 标准化角度到0-360范围
        /// </summary>
        private float NormalizeAngle(float angle)
        {
            angle = angle % 360f;
            if (angle < 0)
            {
                angle += 360f;
            }
            return angle;
        }

        /// <summary>
        /// 判断角度是否在范围内
        /// </summary>
        private bool IsAngleInRange(float angle, float minAngle, float maxAngle)
        {
            if (minAngle <= maxAngle)
            {
                return angle >= minAngle && angle <= maxAngle;
            }
            else
            {
                // 处理跨越0度的情况
                return angle >= minAngle || angle <= maxAngle;
            }
        }

        /// <summary>
        /// 获取默认配置
        /// </summary>
        public static WheelConfig GetDefaultConfig()
        {
            // 尝试从Resources加载配置
            WheelConfig config = Resources.Load<WheelConfig>("Configs/WheelConfig");

            // 如果没有找到，则创建一个默认配置
            if (config == null)
            {
                LogF8.LogWarning("未找到WheelConfig，使用默认配置");
                config = CreateInstance<WheelConfig>();
            }

            return config;
        }
    }
    }
}
