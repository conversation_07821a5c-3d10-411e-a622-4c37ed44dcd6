---
description:
globs:
alwaysApply: true
---
# QFramework 架构规范

## 架构层次
- Controller层 (IController)
- System层 (ISystem)
- Model层 (IModel)
- Utility层 (IUtility)

## 通信规则
1. Controller层：
   - 必须实现IController接口
   - 通过SendCommand发送命令
   - 通过GetModel获取数据
   - 禁止直接修改Model

2. System层：
   - 必须实现ISystem接口
   - 必须实现ICanGetModel接口
   - 负责处理业务逻辑
   - 通过Command修改Model

3. Model层：
   - 必须实现IModel接口
   - 使用BindableProperty
   - 只存储数据，不包含业务逻辑
   - 通过事件通知状态变化

## FSM规范
- 使用QFramework的FSM实现
- 状态类继承AbstractState
- 使用fsm.ChangeState()切换状态
- 分离状态切换条件

## 事件系统
- 事件类必须以Event结尾
- 使用QFramework的事件系统
- 在OnDestroy中注销事件
- 避免事件风暴

## 状态管理规范

### 单一职责原则
- 每个组件的状态只由其自身管理
- 外部系统不得直接修改其他组件的内部状态
- 状态变更必须通过公开的接口进行

### 状态同步规则
- 在关键操作前验证状态一致性
- 添加防重复调用机制（如isMoving标志）
- 记录详细的状态变更日志用于调试

### FSM状态切换时序
- 让每个Manager自己管理其FSM状态切换
- 禁止外部提前切换其他组件的FSM状态
- 正确时序：设置标志位 → 切换FSM状态 → 执行逻辑 → 恢复状态

### 调试最佳实践
- 使用调用堆栈分析来追踪状态变更来源
- 在关键方法中记录调用者信息
- 添加状态快照和时序验证日志
