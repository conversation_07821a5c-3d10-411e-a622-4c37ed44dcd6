using UnityEngine;
using QFramework;
using QHLC.Events;
using System.Collections.Generic;
using F8Framework.Core;

namespace QHLC.Prize
{
    /// <summary>
    /// 奖励信息系统，负责管理奖励信息的显示
    /// </summary>
    public class PrizeInfoSystem : AbstractSystem
    {
        // 奖励图标
        private Sprite coinIcon;
        private Sprite lotteryIcon;
        private Sprite specialIcon;

        // 奖励基础数量
        private int coinBaseAmount = 10;
        private int lotteryBaseAmount = 1;

        // 转盘结果到奖励类型的映射
        private Dictionary<WheelResult, PrizeType> resultToPrizeType = new Dictionary<WheelResult, PrizeType>();

        // ScrollInfoKit管理器对象
        private GameObject scrollInfoManager;

        // 标记ScrollInfoKit是否已经初始化
        private bool isScrollInfoKitInitialized = false;

        protected override void OnInit()
        {
            // 初始化转盘结果到奖励类型的映射
            InitResultToPrizeTypeMapping();

            // 加载奖励图标
            LoadPrizeIcons();

            // 注册潜艇移动完成事件 - 在潜艇到达目的地后显示奖励
            TypeEventSystem.Global.Register<SubmarineMoveCompletedEvent>(OnSubmarineMoveCompleted);

            LogF8.Log("PrizeInfoSystem: 初始化完成");
        }

        /// <summary>
        /// 初始化转盘结果到奖励类型的映射
        /// </summary>
        private void InitResultToPrizeTypeMapping()
        {
            // 根据转盘结果设置奖励类型
            resultToPrizeType[WheelResult.Stop] = PrizeType.None;
            resultToPrizeType[WheelResult.Move1] = PrizeType.Coin;
            resultToPrizeType[WheelResult.Move2] = PrizeType.Coin;
            resultToPrizeType[WheelResult.Move3] = PrizeType.Lottery;
            resultToPrizeType[WheelResult.Move4] = PrizeType.Special;
        }

        /// <summary>
        /// 加载奖励图标
        /// </summary>
        private void LoadPrizeIcons()
        {
            // 创建默认图标
            coinIcon = CreateDefaultSprite(Color.yellow);
            lotteryIcon = CreateDefaultSprite(Color.blue);
            specialIcon = CreateDefaultSprite(Color.red);

            // 记录创建图标信息
            LogF8.Log("PrizeInfoSystem: 创建默认图标");
        }

        /// <summary>
        /// 创建默认图标
        /// </summary>
        private Sprite CreateDefaultSprite(Color color)
        {
            // 创建一个32x32的纹理
            Texture2D texture = new Texture2D(32, 32);

            // 填充颜色
            Color[] colors = new Color[32 * 32];
            for (int i = 0; i < colors.Length; i++)
            {
                colors[i] = color;
            }
            texture.SetPixels(colors);
            texture.Apply();

            // 创建精灵
            return Sprite.Create(texture, new Rect(0, 0, 32, 32), new Vector2(0.5f, 0.5f));
        }

        /// <summary>
        /// 初始化ScrollInfoKit
        /// </summary>
        private void InitScrollInfoKit()
        {
            // 如果已经初始化，直接返回
            if (isScrollInfoKitInitialized)
            {
                return;
            }

            // 如果scrollInfoManager为空，创建一个新的
            if (scrollInfoManager == null)
            {
                // 创建ScrollInfoManager对象
                scrollInfoManager = new GameObject("[ScrollInfoManager]");
                Object.DontDestroyOnLoad(scrollInfoManager);
            }

            // 初始化ScrollInfoKit
            ScrollInfoKit.Init(scrollInfoManager, 5);

            // 标记为已初始化
            isScrollInfoKitInitialized = true;

            LogF8.Log("PrizeInfoSystem: ScrollInfoKit初始化完成");
        }

        /// <summary>
        /// 处理潜艇移动完成事件 - 在潜艇到达目的地后显示奖励
        /// </summary>
        private void OnSubmarineMoveCompleted(SubmarineMoveCompletedEvent evt)
        {
            // 从WheelModel获取转盘结果
            var wheelModel = this.GetModel<WheelModel>();
            if (wheelModel != null)
            {
                WheelResult result = wheelModel.TargetResult.Value;
                LogF8.Log($"PrizeInfoSystem: 潜艇移动完成，转盘结果：{(int)result} ({result})，显示奖励信息");

                // 显示奖励信息
                ShowPrizeInfo(result);
            }
            else
            {
                LogF8.LogWarning("PrizeInfoSystem: 无法获取WheelModel，无法显示奖励信息");
            }
        }

        /// <summary>
        /// 显示奖励信息
        /// </summary>
        public void ShowPrizeInfo(WheelResult result)
        {
            // 如果结果是Stop，不显示奖励信息
            if (result == WheelResult.Stop)
            {
                return;
            }

            // 获取奖励类型
            PrizeType prizeType = resultToPrizeType.ContainsKey(result) ? resultToPrizeType[result] : PrizeType.None;

            // 如果没有奖励，不显示信息
            if (prizeType == PrizeType.None)
            {
                return;
            }

            // 确保ScrollInfoKit已经初始化
            InitScrollInfoKit();

            // 计算奖励数量
            int amount = CalculatePrizeAmount(prizeType, result);

            // 获取奖励图标
            Sprite icon = GetPrizeIcon(prizeType);

            // 创建奖励信息
            PrizeInfo prizeInfo = new PrizeInfo(prizeType, amount, icon);

            // 使用ScrollInfoKit显示奖励信息
            ScrollInfoKit.ShowItemDropInfo(GetPrizeTypeName(prizeType), icon, amount, 3f, 1);

            LogF8.Log($"PrizeInfoSystem: 显示奖励信息：{prizeInfo.Description}");
        }

        /// <summary>
        /// 显示自定义奖励信息 - 供扩展方法调用，避免重复逻辑
        /// </summary>
        public void ShowCustomPrizeInfo(PrizeType type, int amount, Sprite icon = null)
        {
            // 如果没有奖励，不显示信息
            if (type == PrizeType.None)
            {
                return;
            }

            // 确保ScrollInfoKit已经初始化
            InitScrollInfoKit();

            // 使用传入的图标，如果为空则使用系统默认图标
            Sprite displayIcon = icon ?? GetPrizeIcon(type);

            // 创建奖励信息
            PrizeInfo prizeInfo = new(type, amount, displayIcon);

            // 使用ScrollInfoKit显示奖励信息
            ScrollInfoKit.ShowItemDropInfo(GetPrizeTypeName(type), displayIcon, amount, 3f, 1);

            LogF8.Log($"PrizeInfoSystem: 显示自定义奖励信息：{prizeInfo.Description}");
        }

        /// <summary>
        /// 计算奖励数量
        /// </summary>
        private int CalculatePrizeAmount(PrizeType type, WheelResult result)
        {
            int resultValue = (int)result;

            switch (type)
            {
                case PrizeType.Coin:
                    return coinBaseAmount * resultValue;
                case PrizeType.Lottery:
                    return lotteryBaseAmount * resultValue;
                case PrizeType.Special:
                    return resultValue;
                default:
                    return 0;
            }
        }

        /// <summary>
        /// 获取奖励图标
        /// </summary>
        private Sprite GetPrizeIcon(PrizeType type)
        {
            switch (type)
            {
                case PrizeType.Coin:
                    return coinIcon;
                case PrizeType.Lottery:
                    return lotteryIcon;
                case PrizeType.Special:
                    return specialIcon;
                default:
                    return null;
            }
        }

        /// <summary>
        /// 获取奖励类型名称
        /// </summary>
        private string GetPrizeTypeName(PrizeType type)
        {
            switch (type)
            {
                case PrizeType.Coin:
                    return "金币";
                case PrizeType.Lottery:
                    return "彩票";
                case PrizeType.Special:
                    return "特殊奖励";
                default:
                    return "未知奖励";
            }
        }

        /// <summary>
        /// 设置金币基础数量
        /// </summary>
        public void SetCoinBaseAmount(int amount)
        {
            coinBaseAmount = amount;
        }

        /// <summary>
        /// 设置彩票基础数量
        /// </summary>
        public void SetLotteryBaseAmount(int amount)
        {
            lotteryBaseAmount = amount;
        }

        /// <summary>
        /// 设置奖励图标
        /// </summary>
        public void SetPrizeIcons(Sprite coinIcon, Sprite lotteryIcon, Sprite specialIcon)
        {
            this.coinIcon = coinIcon ?? this.coinIcon;
            this.lotteryIcon = lotteryIcon ?? this.lotteryIcon;
            this.specialIcon = specialIcon ?? this.specialIcon;
        }

        /// <summary>
        /// 清除所有奖励信息
        /// </summary>
        public void ClearAllInfo()
        {
            // 确保ScrollInfoKit已经初始化
            if (isScrollInfoKitInitialized)
            {
                ScrollInfoKit.ClearAllInfo();
            }
        }

        /// <summary>
        /// 设置ScrollInfoManager
        /// </summary>
        /// <param name="manager">管理器对象</param>
        public void SetScrollInfoManager(GameObject manager)
        {
            // 如果已经初始化，则不能再设置
            if (isScrollInfoKitInitialized)
            {
                LogF8.LogWarning("PrizeInfoSystem: ScrollInfoKit已经初始化，无法设置新的ScrollInfoManager");
                return;
            }

            scrollInfoManager = manager;
        }
    }
}
