using UnityEngine;
using UnityEngine.UI;
using QFramework;
using F8Framework.Core;
using QHLC;
using QHLC.Views;
using QHLC.Controllers;
using QHLC.Events;
using QHLC.Prize;

public class MainUIView : Mono<PERSON>eh<PERSON>our, IController
{
    [Header("视图组件")]
    [SerializeField] private LightView lightView;
    [SerializeField] private SubmarineView submarineView;
    [SerializeField] private WheelView wheelView;
    [SerializeField] private PrizeInfoView prizeInfoView;

    [Header("教程界面")]
    [SerializeField] private GameObject tutorialPanel; // 教程面板
    [SerializeField] private Button startButton; // 开始按钮

    [Header("UI元素")]
    [SerializeField] private Text wheelBufferCountText; // 转盘缓存次数文本

    [Header("奖励UI位置")]
    [SerializeField] private RectTransform coinUI; // 金币UI位置
    [SerializeField] private RectTransform lotteryUI; // 彩票UI位置
    [SerializeField] private RectTransform wheelUI; // 转盘UI位置
    [SerializeField] private RectTransform gachaUI; // 扭蛋UI位置
    [SerializeField] private RectTransform jpStarUI; // JP大奖UI位置

    /// <summary>
    /// 获取架构
    /// </summary>
    public IArchitecture GetArchitecture()
    {
        return QHLCArchitecture.Interface;
    }

    private void Update()
    {
        HandleInput();
    }

    private void Awake()
    {
        RegisterGameController();

        // 注册必要的UI事件监听
        TypeEventSystem.Global.Register<LightActivatedEvent>(OnLightActivatedHandler);
        TypeEventSystem.Global.Register<WheelBufferCountChangedEvent>(OnWheelBufferCountChangedHandler);

        // 初始化转盘缓存次数显示
        UpdateWheelBufferCountUI(0);
    }

    private void RegisterGameController()
    {
        GameController.Instance.lightView = lightView;
        GameController.Instance.wheelView = wheelView;
        GameController.Instance.submarineView = submarineView;
    }



    // 开始游戏 - 委托给GameController处理
    public void StartGame()
    {
        // 游戏启动逻辑由GameController统一管理
        if (GameController.Instance != null)
        {
            GameController.Instance.StartGame();
        }
        else
        {
            LogF8.LogError("MainUIView: GameController实例未找到，无法启动游戏");
        }
    }

    // 处理用户输入
    private void HandleInput()
    {
        // 处理空格键输入
        if (Input.GetKeyDown(KeyCode.Space))
        {
            // 创建输入事件
            InputEvent inputEvent = new()
            {
                Type = InputType.LightActivation,
                Index = -1 // 使用-1表示随机灯光
            };

            // 记录输入
            var inputBufferSystem = this.GetSystem<InputBufferSystem>();
            if (inputBufferSystem != null)
            {
                inputBufferSystem.RecordInput(inputEvent);
            }
            else if (lightView != null)
            {
                // 如果无法获取InputBufferSystem，直接调用lightView
                lightView.OnSpaceKeyDown();
            }
        }
    }

    // 灯光激活事件处理 - 仅用于UI显示更新
    private void OnLightActivatedHandler(LightActivatedEvent e)
    {
        // 更新灯光状态显示
        lightView.ShowLightAtIndex(e.LightIndex);
    }

    private void OnDestroy()
    {
        // 移除事件监听
        TypeEventSystem.Global.UnRegister<LightActivatedEvent>(OnLightActivatedHandler);
        TypeEventSystem.Global.UnRegister<WheelBufferCountChangedEvent>(OnWheelBufferCountChangedHandler);
    }

    // 转盘缓存次数变化事件处理
    private void OnWheelBufferCountChangedHandler(WheelBufferCountChangedEvent e)
    {
        // 更新UI显示
        UpdateWheelBufferCountUI(e.Count);
    }

    // 更新转盘缓存次数UI
    private void UpdateWheelBufferCountUI(int count)
    {
        if (wheelBufferCountText != null)
        {
            wheelBufferCountText.text = $"{count}x";

            // 如果没有缓存的转盘次数，隐藏UI
            //wheelBufferCountText.gameObject.SetActive(count > 0);
        }
    }

    // 获取UI位置方法

    /// <summary>
    /// 获取金币UI的位置
    /// </summary>
    /// <returns>金币UI的世界坐标</returns>
    public Vector3 GetCoinUIPosition()
    {
        if (coinUI != null)
        {
            return coinUI.position;
        }
        return Vector3.zero;
    }

    /// <summary>
    /// 获取彩票UI的位置
    /// </summary>
    /// <returns>彩票UI的世界坐标</returns>
    public Vector3 GetLotteryUIPosition()
    {
        if (lotteryUI != null)
        {
            return lotteryUI.position;
        }
        return Vector3.zero;
    }

    /// <summary>
    /// 获取转盘UI的位置
    /// </summary>
    /// <returns>转盘UI的世界坐标</returns>
    public Vector3 GetWheelUIPosition()
    {
        if (wheelUI != null)
        {
            return wheelUI.position;
        }
        return Vector3.zero;
    }

    /// <summary>
    /// 获取扭蛋UI的位置
    /// </summary>
    /// <returns>扭蛋UI的世界坐标</returns>
    public Vector3 GetGachaUIPosition()
    {
        if (gachaUI != null)
        {
            return gachaUI.position;
        }
        return Vector3.zero;
    }

    /// <summary>
    /// 获取JP大奖UI的位置
    /// </summary>
    /// <returns>JP大奖UI的世界坐标</returns>
    public Vector3 GetJpStarUIPosition()
    {
        if (jpStarUI != null)
        {
            return jpStarUI.position;
        }
        return Vector3.zero;
    }

    // 自动获取组件（自动生成，不能删除）
}
