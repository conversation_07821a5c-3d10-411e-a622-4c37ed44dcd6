---
description:
globs:
alwaysApply: false
---
# PathController 已知问题和解决方案

## 问题描述

[SubmarineManager.cs](mdc:Assets/Scripts/Runtime/Managers/SubmarineManager.cs) 中的 PathController 存在状态同步问题，这是一个已知的架构问题。

### 具体症状
- `MoveSubmarineStatic()` 在不同调用时机产生不同的移动效果
- PathController 内部位置状态与 `currentStationIndex` 不同步
- 相同的移动步数可能从错误的起始位置开始

## 技术原因

```csharp
// 在 MoveWithPathController 方法中可以看到
ModuleLogManager.LogSubmarineWarning($"PathController内部状态可能与currentStationIndex({currentStationIndex})不同步");
pathController.SetCurrentPosition(currentStationIndex); // 尝试同步，但可能失败
```

## 影响范围

### 受影响的调用
- [WheelSystem.cs](mdc:Assets/Scripts/Runtime/Systems/WheelSystem.cs) 中的转盘触发移动
- [QuickSubmarineTest.cs](mdc:Assets/Scripts/Runtime/Test/QuickSubmarineTest.cs) 中的测试移动
- 任何通过游戏流程触发的潜艇移动

### 不受影响的情况
- 直接使用 DOTween 的移动（但需要手动切换到该模式）

## 解决方案

### 短期解决方案
1. **状态检查**：在调用移动前检查 `IsMoving()` 状态
2. **避免外部FSM管理**：不要在外部提前切换潜艇的FSM状态
3. **使用统一的调用方式**：始终使用 `SubmarineManager.MoveSubmarineStatic()`

### 长期解决方案
1. **完全使用DOTween**：修改SubmarineManager强制使用 `MoveWithDOTween`
2. **重构PathController**：解决状态同步的根本问题
3. **状态机重设计**：简化状态管理复杂度

## 代码示例

### ✅ 正确的调用方式
```csharp
// 检查状态
if (SubmarineManager.Instance != null && !SubmarineManager.Instance.IsMoving())
{
    // 让SubmarineManager自己管理状态切换
    SubmarineManager.MoveSubmarineStatic(steps, onComplete);
}
```

### ❌ 错误的调用方式
```csharp
// 不要这样做
GameFSMCoordinator.Instance.ChangeState(GameStateType.SubmarineMoving);
SubmarineManager.MoveSubmarineStatic(steps, onComplete);
```

## 监控和调试

### 关键日志
- 观察 `currentStationIndex` 值的变化
- 监控 PathController 状态同步尝试的成功/失败
- 检查调用堆栈信息以识别调用来源

### 问题识别
```csharp
// 如果看到这个警告，说明遇到了状态同步问题
"PathController内部状态可能与currentStationIndex不同步，这可能导致路径差异"
```

## 预防措施

1. **统一调用接口**：所有潜艇移动都通过 SubmarineManager
2. **状态封装**：不暴露内部状态管理细节
3. **防御性编程**：在移动前总是检查状态一致性
4. **详细日志**：记录所有状态变更和调用来源

## 相关文件

- 核心管理器：[SubmarineManager.cs](mdc:Assets/Scripts/Runtime/Managers/SubmarineManager.cs)
- 问题调用：[WheelSystem.cs](mdc:Assets/Scripts/Runtime/Systems/WheelSystem.cs)
- 正常调用：[QuickSubmarineTest.cs](mdc:Assets/Scripts/Runtime/Test/QuickSubmarineTest.cs)

## 更新历史

- **2024**: 发现并记录PathController状态同步问题
- **2024**: 在WheelSystem中添加状态检查机制
- **2024**: 移除外部FSM状态管理，改为内部自管理

