using UnityEngine;
using UnityEngine.UI;
using QFramework;
using DG.Tweening;
using Sirenix.OdinInspector;
using QHLC.Events;
using Sequence = DG.Tweening.Sequence;
using F8Framework.Core;
using QHLC.Utilities;
using QHLC.ScriptableObjects;
using System.Collections;

namespace QHLC.Views
{
    /// <summary>
    /// 转盘视图，负责转盘旋转的视觉效果
    /// 使用QFramework架构重构
    /// </summary>
    public class WheelView : MonoBehaviour, IController
    {
        [Header("转盘设置")]
        [SerializeField] private RectTransform wheelRectTransform;
        [SerializeField] private RectTransform pointerRectTransform;
        [SerializeField] private Image[] greenFlashEffects;
        [SerializeField] private WheelConfig wheelConfig;

        private Sequence wheelRotationSequence;
        private Sequence flashSequence;
        private Sequence idleRotationSequence;
        private float currentVisualAngle;
        private readonly float idleRotationSpeed = 10f;

        /// <summary>
        /// 闪烁超时检测协程
        /// </summary>
        private Coroutine flashTimeoutCoroutine;

        private void Awake()
        {
            // 注册QFramework事件
            TypeEventSystem.Global.Register<WheelSpinningStartEvent>(OnWheelSpinningStart);
            TypeEventSystem.Global.Register<WheelFlashingStartEvent>(OnWheelFlashingStart);
            TypeEventSystem.Global.Register<WheelResetEvent>(OnWheelReset);
            TypeEventSystem.Global.Register<StartWheelSpinningEvent>(OnStartWheelSpinning);

            // 如果没有设置配置，加载默认配置
            if (wheelConfig == null)
            {
                wheelConfig = WheelConfig.GetDefaultConfig();
                ModuleLogManager.LogWheel("WheelView: 使用默认WheelConfig配置");
            }
        }

        private void Start()
        {
            // 初始化转盘设置
            InitializeWheelSettings();

            // 重置转盘
            this.SendCommand<ResetWheelCommand>();
        }

        private void OnDestroy()
        {
            // 注销QFramework事件
            TypeEventSystem.Global.UnRegister<WheelSpinningStartEvent>(OnWheelSpinningStart);
            TypeEventSystem.Global.UnRegister<WheelFlashingStartEvent>(OnWheelFlashingStart);
            TypeEventSystem.Global.UnRegister<WheelResetEvent>(OnWheelReset);
            TypeEventSystem.Global.UnRegister<StartWheelSpinningEvent>(OnStartWheelSpinning);

            // 取消所有动画
            if (wheelRotationSequence != null)
            {
                wheelRotationSequence.Kill();
                wheelRotationSequence = null;
            }

            if (flashSequence != null)
            {
                flashSequence.Kill();
                flashSequence = null;
            }

            // 添加闲置旋转动画的清理
            if (idleRotationSequence != null)
            {
                idleRotationSequence.Kill();
                idleRotationSequence = null;
            }

            // 清理超时检测协程
            StopFlashingTimeout();
        }

        public RectTransform GetWheelRectTransform()
        {
            return wheelRectTransform;
        }

        /// <summary>
        /// 处理开始转盘旋转事件
        /// </summary>
        private void OnStartWheelSpinning(StartWheelSpinningEvent e)
        {
            ModuleLogManager.LogWheel($"WheelView.OnStartWheelSpinning: 收到开始转盘旋转事件，目标结果: {e.TargetResult} ({(int)e.TargetResult})");
            this.SendCommand(new StartWheelSpinningCommand(e.TargetResult));
        }

        /// <summary>
        /// 初始化转盘设置
        /// </summary>
        private void InitializeWheelSettings()
        {
            var model = this.GetModel<WheelModel>();

            // 输出当前使用的参数值
            ModuleLogManager.LogWheel($"WheelView: Using wheel settings from config - AccelerationDuration: {wheelConfig.accelerationDuration}, SpinDuration: {wheelConfig.spinDuration}, SlowDownDuration: {wheelConfig.slowDownDuration}");
            ModuleLogManager.LogWheel($"WheelView: Using wheel settings from config - FlashDuration: {wheelConfig.flashDuration}, InitialAngle: {wheelConfig.initialAngle}, AdditionalRotations: {wheelConfig.additionalRotations}");

            // 只在第一次初始化时设置初始角度
            if (wheelRectTransform != null && model.CurrentAngle.Value == 0f)
            {
                wheelRectTransform.localRotation = Quaternion.Euler(0, 0, wheelConfig.initialAngle);
                model.CurrentAngle.Value = wheelConfig.initialAngle;
            }

            // 隐藏所有闪烁效果
            HideAllFlashEffects();
        }

        /// <summary>
        /// 处理转盘旋转开始事件
        /// </summary>
        private void OnWheelSpinningStart(WheelSpinningStartEvent e)
        {
            var model = this.GetModel<WheelModel>();
            var utility = this.GetUtility<WheelUtility>();

            // 从事件中提取目标结果
            WheelResult targetResult = e.TargetResult;

            // 取消之前的动画
            if (wheelRotationSequence != null)
            {
                wheelRotationSequence.Kill();
                wheelRotationSequence = null;
            }

            // 隐藏所有闪烁效果
            HideAllFlashEffects();

            // 获取目标角度
            float targetAngle = wheelConfig.GetResultAngle(targetResult);

            // 记录当前转盘的实际角度
            float actualCurrentAngle = wheelRectTransform.localRotation.eulerAngles.z;
            ModuleLogManager.LogWheel($"WheelView.OnWheelSpinningStart: 转盘当前实际角度: {actualCurrentAngle}, 模型中的角度: {model.CurrentAngle.Value}");

            // 创建旋转动画
            wheelRotationSequence = utility.CreateWheelRotationAnimation(
                wheelRectTransform,
                targetAngle,
                wheelConfig
            );

            // 设置动画完成回调
            wheelRotationSequence.OnComplete(() => {
                // 获取轮盘的实际角度
                float actualAngle = wheelRectTransform.localRotation.eulerAngles.z;

                // 标准化角度到0-360范围
                float normalizedActualAngle = (actualAngle % 360f + 360f) % 360f;

                // 更新当前角度为实际角度，而不是原始的目标角度
                model.CurrentAngle.Value = normalizedActualAngle;

                ModuleLogManager.LogWheel($"WheelView.OnComplete: 使用实际角度更新模型 - 实际角度: {normalizedActualAngle}, 原始目标角度: {targetAngle}");

                // 检查实际角度对应的结果是否与目标结果一致
                WheelResult actualResult = wheelConfig.GetResultFromAngle(normalizedActualAngle);
                WheelResult expectedResult = e.TargetResult;

                if (actualResult != expectedResult)
                {
                    ModuleLogManager.LogWheelWarning($"WheelView.OnComplete: 实际结果与预期结果不一致！实际: {actualResult}, 预期: {expectedResult}");
                }

                // 通知系统旋转完成
                this.GetSystem<WheelSystem>().OnSpinningCompleted();
            });

            // 播放动画
            wheelRotationSequence.Play();
        }

        /// <summary>
        /// 处理转盘闪烁开始事件
        /// </summary>
        private void OnWheelFlashingStart(WheelFlashingStartEvent e)
        {
            var utility = this.GetUtility<WheelUtility>();

            // 从事件中提取结果
            WheelResult result = e.Result;
            ModuleLogManager.LogWheel($"WheelView.OnWheelFlashingStart: 开始处理闪烁，结果: {result} ({(int)result})");

            // 取消之前的动画
            if (flashSequence != null)
            {
                ModuleLogManager.LogWheel("WheelView.OnWheelFlashingStart: 取消之前的闪烁动画");
                flashSequence.Kill();
                flashSequence = null;
            }

            // 停止之前的超时检测
            StopFlashingTimeout();

            // 获取对应结果的闪烁效果索引
            int flashIndex = utility.GetFlashEffectIndex(result);
            ModuleLogManager.LogWheel($"WheelView.OnWheelFlashingStart: 闪烁效果索引: {flashIndex}, greenFlashEffects数组长度: {greenFlashEffects?.Length ?? 0}");

            // 确保索引有效
            if (flashIndex >= 0 && flashIndex < greenFlashEffects.Length)
            {
                ModuleLogManager.LogWheel($"WheelView.OnWheelFlashingStart: 索引有效，开始创建闪烁动画");

                // 创建闪烁动画
                flashSequence = utility.CreateFlashingAnimation(
                    greenFlashEffects[flashIndex],
                    wheelConfig
                );

                if (flashSequence != null)
                {
                    ModuleLogManager.LogWheel("WheelView.OnWheelFlashingStart: 闪烁动画创建成功，设置完成回调");

                    // 设置动画完成回调
                    flashSequence.OnComplete(() => {
                        ModuleLogManager.LogWheel($"WheelView.OnFlashingComplete: 闪烁动画完成回调被调用，结果: {result} ({(int)result})");
                        StopFlashingTimeout(); // 停止超时检测
                        this.GetSystem<WheelSystem>().OnFlashingCompleted();
                    });

                    // 播放动画
                    ModuleLogManager.LogWheel("WheelView.OnWheelFlashingStart: 开始播放闪烁动画");
                    flashSequence.Play();

                    // 启动超时检测
                    StartFlashingTimeout(result);
                }
                else
                {
                    ModuleLogManager.LogWheelError("WheelView.OnWheelFlashingStart: 闪烁动画创建失败，直接调用完成回调");
                    this.GetSystem<WheelSystem>().OnFlashingCompleted();
                }
            }
            else
            {
                ModuleLogManager.LogWheelError($"WheelView.OnWheelFlashingStart: 索引无效 (flashIndex: {flashIndex}, 数组长度: {greenFlashEffects?.Length ?? 0})，直接调用完成回调");
                this.GetSystem<WheelSystem>().OnFlashingCompleted();
            }
        }

        /// <summary>
        /// 处理转盘重置事件
        /// </summary>
        private void OnWheelReset(WheelResetEvent e)
        {
            // 取消所有动画
            if (wheelRotationSequence != null)
            {
                wheelRotationSequence.Kill();
                wheelRotationSequence = null;
            }

            if (flashSequence != null)
            {
                flashSequence.Kill();
                flashSequence = null;
            }

            // 停止超时检测
            StopFlashingTimeout();

            // 隐藏所有闪烁效果
            HideAllFlashEffects();
        }

        /// <summary>
        /// 隐藏所有闪烁效果
        /// </summary>
        private void HideAllFlashEffects()
        {
            foreach (var effect in greenFlashEffects)
            {
                if (effect != null)
                {
                    effect.gameObject.SetActive(false);
                }
            }
        }

        public IArchitecture GetArchitecture()
        {
            return QHLCArchitecture.Interface;
        }

        /// <summary>
        /// 开始闲置旋转动画
        /// </summary>
        public void StartIdleRotation()
        {
            if (wheelRectTransform == null) return;

            StopIdleRotation(); // 先停止之前的动画

            // 获取当前角度
            currentVisualAngle = wheelRectTransform.localRotation.eulerAngles.z;

            idleRotationSequence = DOTween.Sequence();
            float rotationDuration = 360f / idleRotationSpeed;

            idleRotationSequence.Append(
                wheelRectTransform.DOLocalRotate(
                    new Vector3(0, 0, currentVisualAngle - 360f),
                    rotationDuration,
                    RotateMode.FastBeyond360
                ).SetEase(DG.Tweening.Ease.Linear)
            );

            idleRotationSequence.OnComplete(() => {
                currentVisualAngle = NormalizeAngle(currentVisualAngle - 360f);
                StartIdleRotation(); // 循环旋转
            });

            idleRotationSequence.Play();
            ModuleLogManager.LogWheel("WheelView: 开始闲置旋转动画");
        }

        /// <summary>
        /// 停止闲置旋转动画
        /// </summary>
        public void StopIdleRotation()
        {
            if (idleRotationSequence != null)
            {
                idleRotationSequence.Kill();
                idleRotationSequence = null;
                ModuleLogManager.LogWheel("WheelView: 停止闲置旋转动画");
            }
        }

        /// <summary>
        /// 标准化角度到0-360范围
        /// </summary>
        private float NormalizeAngle(float angle)
        {
            angle = angle % 360f;
            if (angle < 0) angle += 360f;
            return angle;
        }

        /// <summary>
        /// 启动闪烁超时检测
        /// </summary>
        private void StartFlashingTimeout(WheelResult result)
        {
            StopFlashingTimeout(); // 先停止之前的超时检测

            float timeoutDuration = wheelConfig.flashDuration + 1f; // 比预期时间多1秒
            flashTimeoutCoroutine = StartCoroutine(FlashingTimeoutCoroutine(result, timeoutDuration));

            ModuleLogManager.LogWheel($"WheelView.StartFlashingTimeout: 启动超时检测，超时时间: {timeoutDuration}秒");
        }

        /// <summary>
        /// 停止闪烁超时检测
        /// </summary>
        private void StopFlashingTimeout()
        {
            if (flashTimeoutCoroutine != null)
            {
                StopCoroutine(flashTimeoutCoroutine);
                flashTimeoutCoroutine = null;
                ModuleLogManager.LogWheel("WheelView.StopFlashingTimeout: 停止超时检测");
            }
        }

        /// <summary>
        /// 闪烁超时检测协程
        /// </summary>
        private System.Collections.IEnumerator FlashingTimeoutCoroutine(WheelResult result, float timeoutDuration)
        {
            yield return new WaitForSeconds(timeoutDuration);

            // 如果到达这里，说明闪烁动画超时了
            ModuleLogManager.LogWheelError($"WheelView.FlashingTimeout: 闪烁动画超时！结果: {result}, 超时时间: {timeoutDuration}秒");

            // 检查动画状态
            if (flashSequence != null)
            {
                ModuleLogManager.LogWheelError($"WheelView.FlashingTimeout: 动画序列仍然存在，状态: IsActive={flashSequence.IsActive()}, IsPlaying={flashSequence.IsPlaying()}");

                // 强制终止动画
                flashSequence.Kill();
                flashSequence = null;
            }

            // 强制调用完成回调
            ModuleLogManager.LogWheelError("WheelView.FlashingTimeout: 强制调用闪烁完成回调");
            this.GetSystem<WheelSystem>().OnFlashingCompleted();

            flashTimeoutCoroutine = null;
        }
    }
}
