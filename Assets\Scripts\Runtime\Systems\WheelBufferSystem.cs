using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using QFramework;
using F8Framework.Core;
using QHLC.Events;

namespace QHLC
{
    /// <summary>
    /// 转盘缓存系统，负责管理转盘次数缓存和控制转盘驱动逻辑
    /// </summary>
    public class WheelBufferSystem : AbstractSystem
    {
        protected override void OnInit()
        {
            // 系统初始化
            LogF8.Log("WheelBufferSystem: 初始化");
        }

        /// <summary>
        /// 增加缓存的转盘次数
        /// </summary>
        /// <param name="count">要增加的次数</param>
        public void AddBufferedSpinCount(int count)
        {
            if (count <= 0)
            {
                LogF8.LogWarning($"WheelBufferSystem: 尝试增加无效的转盘次数: {count}");
                return;
            }

            var model = this.GetModel<WheelModel>();
            int currentCount = model.BufferedSpinCount.Value;
            int newCount = currentCount + count;
            model.BufferedSpinCount.Value = newCount;

            LogF8.Log($"WheelBufferSystem: 增加转盘次数 {count}，当前缓存次数: {newCount}");

            // 发送事件通知转盘次数变化
            TypeEventSystem.Global.Send(new WheelBufferCountChangedEvent { Count = newCount });
        }

        /// <summary>
        /// 减少缓存的转盘次数
        /// </summary>
        /// <param name="count">要减少的次数</param>
        /// <returns>是否成功减少（如果缓存次数不足，返回false）</returns>
        public bool ConsumeBufferedSpinCount(int count = 1)
        {
            if (count <= 0)
            {
                LogF8.LogWarning($"WheelBufferSystem: 尝试消耗无效的转盘次数: {count}");
                return false;
            }

            var model = this.GetModel<WheelModel>();
            int currentCount = model.BufferedSpinCount.Value;

            if (currentCount < count)
            {
                LogF8.LogWarning($"WheelBufferSystem: 缓存的转盘次数不足，当前: {currentCount}，需要: {count}");
                return false;
            }

            int newCount = currentCount - count;
            model.BufferedSpinCount.Value = newCount;

            LogF8.Log($"WheelBufferSystem: 消耗转盘次数 {count}，剩余缓存次数: {newCount}");

            // 发送事件通知转盘次数变化
            TypeEventSystem.Global.Send(new WheelBufferCountChangedEvent { Count = newCount });

            return true;
        }

        /// <summary>
        /// 获取当前缓存的转盘次数
        /// </summary>
        /// <returns>缓存的转盘次数</returns>
        public int GetBufferedSpinCount()
        {
            var model = this.GetModel<WheelModel>();
            return model.BufferedSpinCount.Value;
        }

        /// <summary>
        /// 检查是否有缓存的转盘次数
        /// </summary>
        /// <returns>是否有缓存的转盘次数</returns>
        public bool HasBufferedSpins()
        {
            return GetBufferedSpinCount() > 0;
        }

        /// <summary>
        /// 驱动转盘旋转（如果有缓存的转盘次数）
        /// </summary>
        /// <returns>是否成功驱动转盘旋转</returns>
        public bool DriveWheelSpinning()
        {
            // 检查是否有缓存的转盘次数
            if (!HasBufferedSpins())
            {
                LogF8.LogWarning("WheelBufferSystem: 没有缓存的转盘次数，无法驱动转盘旋转");
                return false;
            }

            // 获取转盘系统
            var wheelSystem = this.GetSystem<WheelSystem>();
            var model = this.GetModel<WheelModel>();

            // 如果转盘正在旋转，忽略旋转请求
            if (model.IsSpinning.Value)
            {
                // 使用更详细的日志信息，但降低日志级别避免污染
                LogF8.Log("WheelBufferSystem: 转盘正在旋转，忽略旋转请求");
                return false;
            }

            LogF8.Log($"WheelBufferSystem: 开始驱动转盘旋转，当前缓存次数: {GetBufferedSpinCount()}");

            // 🔧 获取随机转盘结果，排除Stop结果以避免浪费缓存次数
            LogF8.Log("WheelBufferSystem.DriveWheelSpinning: 调用wheelSystem.GetRandomWheelResult(excludeStop: true)");
            WheelResult result = wheelSystem.GetRandomWheelResult(excludeStop: true);
            LogF8.Log($"WheelBufferSystem.DriveWheelSpinning: 生成随机转盘结果(排除Stop): {result} ({(int)result})");

            // 消耗转盘缓存次数（只有在确定不是Stop结果后才消耗）
            if (!ConsumeBufferedSpinCount(1))
            {
                LogF8.LogError("WheelBufferSystem: 消耗转盘缓存次数失败");
                return false;
            }

            // 将随机结果设置到模型中，确保其他组件可以访问
            model.TargetResult.Value = result;
            LogF8.Log($"WheelBufferSystem.DriveWheelSpinning: 设置模型中的TargetResult为: {result} ({(int)result})");

            // 开始旋转转盘
            LogF8.Log($"WheelBufferSystem.DriveWheelSpinning: 驱动转盘旋转，目标结果: {result}，剩余缓存次数: {GetBufferedSpinCount()}");
            wheelSystem.StartSpinning(result);

            return true;
        }

        /// <summary>
        /// 重置缓存的转盘次数
        /// </summary>
        public void ResetBufferedSpinCount()
        {
            var model = this.GetModel<WheelModel>();
            model.BufferedSpinCount.Value = 0;

            LogF8.Log("WheelBufferSystem: 重置缓存的转盘次数");

            // 发送事件通知转盘次数变化
            TypeEventSystem.Global.Send(new WheelBufferCountChangedEvent { Count = 0 });
        }
    }
}
