using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using QFramework;
using QHLC.Models;
using QHLC.Events;
using QHLC.Common;
using QHLC.ScriptableObjects;
using QHLC.Enums;
using F8Framework.Core;
using QHLC.Utilities;

namespace QHLC.Systems
{
    /// <summary>
    /// 建筑数值控制系统，管理建筑数据的初始化和控制
    /// </summary>
    public class BuildingValueSystem : AbstractSystem
    {
        // 建筑数据配置
        private class BuildingValueConfig
        {
            // 普通建筑和停车场建筑的比例，例如3表示每3个普通建筑放置1个停车场建筑
            public int ParkingBuildingRatio = 3;

            // 每个建筑的奖励数量范围
            public Vector2Int RewardCountRange = new Vector2Int(1, 1);

            // 金币奖励数量范围
            public Vector2Int CoinRewardAmountRange = new Vector2Int(1, 2);

            // 其他奖励数量范围
            public Vector2Int OtherRewardAmountRange = new Vector2Int(1, 5);

            // 奖励位置偏移范围
            public Vector2 RewardPositionOffsetRange = new Vector2(0, 0);
        }

        // 建筑数值配置
        private BuildingValueConfig buildingValueConfig = new BuildingValueConfig();

        protected override void OnInit()
        {
            // 注册事件
            this.RegisterEvent<SubmarinePathInitializedEvent>(OnSubmarinePathInitialized);

            // 初始化建筑数值配置
            InitializeBuildingValueConfig();

            ModuleLogManager.Log("BuildingValueSystem: 初始化完成");
        }

        /// <summary>
        /// 初始化建筑数值配置
        /// </summary>
        private void InitializeBuildingValueConfig()
        {
            // 从游戏数值控制系统获取配置值
            var gameValueSystem = this.GetSystem<GameValueSystem>();

            // 如果游戏数值控制系统中已经注册了相关值，则使用这些值
            // 否则，使用默认值并注册到游戏数值控制系统中

            // 普通建筑和停车场建筑的比例
            var parkingRatioValue = gameValueSystem.GetGameValue("BuildingParkingRatio");
            if (parkingRatioValue == null)
            {
                parkingRatioValue = gameValueSystem.RegisterValue("BuildingParkingRatio", buildingValueConfig.ParkingBuildingRatio, 1, 10, 1, true);
            }
            buildingValueConfig.ParkingBuildingRatio = (int)parkingRatioValue.Value;

            // 每个建筑的奖励数量范围
            var rewardMinCountValue = gameValueSystem.GetGameValue("BuildingRewardMinCount");
            if (rewardMinCountValue == null)
            {
                rewardMinCountValue = gameValueSystem.RegisterValue("BuildingRewardMinCount", buildingValueConfig.RewardCountRange.x, 1, 5, 1, true);
            }

            var rewardMaxCountValue = gameValueSystem.GetGameValue("BuildingRewardMaxCount");
            if (rewardMaxCountValue == null)
            {
                rewardMaxCountValue = gameValueSystem.RegisterValue("BuildingRewardMaxCount", buildingValueConfig.RewardCountRange.y, 1, 10, 1, true);
            }

            buildingValueConfig.RewardCountRange = new Vector2Int(
                (int)rewardMinCountValue.Value,
                (int)rewardMaxCountValue.Value
            );

            // 金币奖励数量范围
            var coinRewardMinValue = gameValueSystem.GetGameValue("CoinRewardMinAmount");
            if (coinRewardMinValue == null)
            {
                coinRewardMinValue = gameValueSystem.RegisterValue("CoinRewardMinAmount", buildingValueConfig.CoinRewardAmountRange.x, 1, 1000, 1, true);
            }

            var coinRewardMaxValue = gameValueSystem.GetGameValue("CoinRewardMaxAmount");
            if (coinRewardMaxValue == null)
            {
                coinRewardMaxValue = gameValueSystem.RegisterValue("CoinRewardMaxAmount", buildingValueConfig.CoinRewardAmountRange.y, 1, 1000, 1, true);
            }

            buildingValueConfig.CoinRewardAmountRange = new Vector2Int(
                (int)coinRewardMinValue.Value,
                (int)coinRewardMaxValue.Value
            );

            // 其他奖励数量范围
            var otherRewardMinValue = gameValueSystem.GetGameValue("OtherRewardMinAmount");
            if (otherRewardMinValue == null)
            {
                otherRewardMinValue = gameValueSystem.RegisterValue("OtherRewardMinAmount", buildingValueConfig.OtherRewardAmountRange.x, 1, 100, 1, true);
            }

            var otherRewardMaxValue = gameValueSystem.GetGameValue("OtherRewardMaxAmount");
            if (otherRewardMaxValue == null)
            {
                otherRewardMaxValue = gameValueSystem.RegisterValue("OtherRewardMaxAmount", buildingValueConfig.OtherRewardAmountRange.y, 1, 100, 1, true);
            }

            buildingValueConfig.OtherRewardAmountRange = new Vector2Int(
                (int)otherRewardMinValue.Value,
                (int)otherRewardMaxValue.Value
            );

            // 奖励位置偏移范围
            var rewardOffsetXValue = gameValueSystem.GetGameValue("RewardPositionOffsetX");
            if (rewardOffsetXValue == null)
            {
                rewardOffsetXValue = gameValueSystem.RegisterValue("RewardPositionOffsetX", buildingValueConfig.RewardPositionOffsetRange.x, 10, 100, 5, true);
            }

            var rewardOffsetYValue = gameValueSystem.GetGameValue("RewardPositionOffsetY");
            if (rewardOffsetYValue == null)
            {
                rewardOffsetYValue = gameValueSystem.RegisterValue("RewardPositionOffsetY", buildingValueConfig.RewardPositionOffsetRange.y, 10, 100, 5, true);
            }

            buildingValueConfig.RewardPositionOffsetRange = new Vector2(
                rewardOffsetXValue.Value,
                rewardOffsetYValue.Value
            );

            ModuleLogManager.Log($"BuildingValueSystem: 初始化建筑数值配置完成，停车场比例={buildingValueConfig.ParkingBuildingRatio}，奖励数量范围={buildingValueConfig.RewardCountRange}，金币奖励范围={buildingValueConfig.CoinRewardAmountRange}，其他奖励范围={buildingValueConfig.OtherRewardAmountRange}，奖励位置偏移范围={buildingValueConfig.RewardPositionOffsetRange}");
        }

        /// <summary>
        /// 处理潜艇路径初始化事件
        /// </summary>
        /// <param name="e">事件数据</param>
        private void OnSubmarinePathInitialized(SubmarinePathInitializedEvent e)
        {
            // 初始化建筑数据
            InitializeBuildings(e.PathItems);
        }

        /// <summary>
        /// 初始化建筑数据
        /// </summary>
        /// <param name="pathItems">路径点列表</param>
        private void InitializeBuildings(List<PathItem> pathItems)
        {
            var buildingModel = this.GetModel<BuildingModel>();
            var rewardModel = this.GetModel<RewardModel>();

            // 获取所有站点
            var stationPathItems = pathItems.FindAll(p => p.isStation);

            if (stationPathItems.Count == 0)
            {
                ModuleLogManager.LogError("BuildingValueSystem: 没有找到任何站点");
                return;
            }

            // 生成建筑数据
            int buildingCount = 0;
            int rewardCount = 0;

            for (int i = 0; i < stationPathItems.Count; i++)
            {
                // 确定建筑类型：每N个站点放置一个停车场建筑，其余为普通建筑
                BuildingType buildingType = (i % buildingValueConfig.ParkingBuildingRatio == 0) ? BuildingType.ParkingBuilding : BuildingType.NormalBuilding;

                // 获取配置服务
                var configService = this.GetUtility<ConfigurationService>();
                var buildingConfig = configService.GetConfig<ScriptableObjects.BuildingConfig>();

                // 检查普通建筑精灵列表是否为空
                if (buildingType == BuildingType.NormalBuilding &&
                    (buildingConfig == null || buildingConfig.normalBuildingSprites == null || buildingConfig.normalBuildingSprites.Count == 0))
                {
                    ModuleLogManager.LogWarning("BuildingValueSystem: 普通建筑精灵列表为空，将使用默认建筑类型");
                }

                // 创建建筑
                var building = new Building
                {
                    Id = i + 1, // 建筑ID从1开始
                    Type = buildingType,
                    StationIndex = stationPathItems[i].stationIndex,
                    Position = Vector2.zero, // 位置由BuildingController根据站点位置设置
                    IsActive = true,
                    Name = buildingType == BuildingType.ParkingBuilding ? $"停车场{i/buildingValueConfig.ParkingBuildingRatio + 1}" : $"建筑{i + 1}",
                    Description = buildingType == BuildingType.ParkingBuilding ? "可以停放车辆的场所" : "普通的建筑"
                };

                buildingModel.AddBuilding(building);
                buildingCount++;

                // 为每个建筑生成随机奖励
                int rewardNum = Random.Range(buildingValueConfig.RewardCountRange.x, buildingValueConfig.RewardCountRange.y + 1);
                for (int j = 0; j < rewardNum; j++)
                {
                    // 随机奖励类型
                    RewardType rewardType = (RewardType)Random.Range(1, 3); // 1=Coin, 2=Lottery, 3=WheelSpin, 4=Gacha, 5=JpStar

                    // 随机奖励数量：金币和其他奖励的数量范围不同
                    int amount;
                    if (rewardType == RewardType.Coin)
                    {
                        amount = Random.Range(buildingValueConfig.CoinRewardAmountRange.x, buildingValueConfig.CoinRewardAmountRange.y + 1);
                    }
                    else
                    {
                        amount = Random.Range(buildingValueConfig.OtherRewardAmountRange.x, buildingValueConfig.OtherRewardAmountRange.y + 1);
                    }

                    // 随机位置偏移，确保奖励不重叠
                    Vector2 positionOffset = new Vector2(
                        Random.Range(-buildingValueConfig.RewardPositionOffsetRange.x, buildingValueConfig.RewardPositionOffsetRange.x),
                        Random.Range(-buildingValueConfig.RewardPositionOffsetRange.y, buildingValueConfig.RewardPositionOffsetRange.y)
                    );

                    // 创建奖励
                    var reward = new Reward
                    {
                        Id = rewardCount + 1, // 奖励ID从1开始
                        Type = rewardType,
                        Amount = amount,
                        Position = positionOffset,
                        IsCollected = false,
                        Name = GetRewardName(rewardType),
                        Description = GetRewardDescription(rewardType)
                    };

                    rewardModel.AddReward(reward, building.Id);
                    rewardCount++;
                }
            }

            // 发送建筑初始化完成事件
            this.SendEvent(new BuildingsInitializedEvent { BuildingCount = buildingCount, RewardCount = rewardCount });

            ModuleLogManager.Log($"BuildingValueSystem: 初始化了 {buildingCount} 个建筑和 {rewardCount} 个奖励");
        }

        /// <summary>
        /// 获取奖励名称
        /// </summary>
        /// <param name="type">奖励类型</param>
        /// <returns>奖励名称</returns>
        private string GetRewardName(RewardType type)
        {
            switch (type)
            {
                case RewardType.Coin:
                    return "金币";
                case RewardType.Lottery:
                    return "彩票";
                case RewardType.WheelSpin:
                    return "转盘";
                case RewardType.Gacha:
                    return "扭蛋";
                case RewardType.JpStar:
                    return "JP星星";
                default:
                    return "未知奖励";
            }
        }

        /// <summary>
        /// 获取奖励描述
        /// </summary>
        /// <param name="type">奖励类型</param>
        /// <returns>奖励描述</returns>
        private string GetRewardDescription(RewardType type)
        {
            switch (type)
            {
                case RewardType.Coin:
                    return "可用于购买道具和升级";
                case RewardType.Lottery:
                    return "可用于抽取彩票奖励";
                case RewardType.WheelSpin:
                    return "可用于转动幸运转盘";
                case RewardType.Gacha:
                    return "可用于扭蛋机抽奖";
                case RewardType.JpStar:
                    return "收集足够数量可获得JP大奖";
                default:
                    return "未知奖励描述";
            }
        }
    }
}
