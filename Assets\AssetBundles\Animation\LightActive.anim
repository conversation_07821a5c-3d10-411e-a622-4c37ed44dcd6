%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: LightActive
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves:
  - serializedVersion: 2
    curve:
    - time: 0
      value: {fileID: 21300000, guid: 7499800ba46cc88469cb770010bdad98, type: 3}
    - time: 0.041666668
      value: {fileID: 21300000, guid: 3a7d497ca6423b840b25d129ec9951f6, type: 3}
    - time: 0.083333336
      value: {fileID: 21300000, guid: 01fc45b247b54b242b5f31dfe4cb96b7, type: 3}
    - time: 0.125
      value: {fileID: 21300000, guid: c44904d5debd4da44bdcfe55b36f3809, type: 3}
    - time: 0.16666667
      value: {fileID: 21300000, guid: 4479d9af53d36004090bd50912c9d864, type: 3}
    - time: 0.20833333
      value: {fileID: 21300000, guid: 6e5dc5fe759dd8041a5b465d979a0864, type: 3}
    - time: 0.25
      value: {fileID: 21300000, guid: c14dab27414a1e749825d7312c0f5dcf, type: 3}
    - time: 0.29166666
      value: {fileID: 21300000, guid: 6abbbea265625934e9be2aa26b83518c, type: 3}
    - time: 0.33333334
      value: {fileID: 21300000, guid: 321a88fc5cf0459428eb1ca372127364, type: 3}
    - time: 0.375
      value: {fileID: 21300000, guid: 32c5fd327796d72439f1913d4cd6dd7f, type: 3}
    - time: 0.41666666
      value: {fileID: 21300000, guid: fb2fd6e505a31d1448766510d400f74c, type: 3}
    - time: 0.45833334
      value: {fileID: 21300000, guid: 6469790216861cc41bd8325eb0b9c01a, type: 3}
    - time: 0.5
      value: {fileID: 21300000, guid: f2280a5780ce60a45905b1025c9ca8ba, type: 3}
    - time: 0.5416667
      value: {fileID: 21300000, guid: e120fe40fce1a234b878cc0a3a194864, type: 3}
    attribute: m_Sprite
    path: 
    classID: 114
    script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
    flags: 2
  m_SampleRate: 24
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 2015549526
      script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 1
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping:
    - {fileID: 21300000, guid: 7499800ba46cc88469cb770010bdad98, type: 3}
    - {fileID: 21300000, guid: 3a7d497ca6423b840b25d129ec9951f6, type: 3}
    - {fileID: 21300000, guid: 01fc45b247b54b242b5f31dfe4cb96b7, type: 3}
    - {fileID: 21300000, guid: c44904d5debd4da44bdcfe55b36f3809, type: 3}
    - {fileID: 21300000, guid: 4479d9af53d36004090bd50912c9d864, type: 3}
    - {fileID: 21300000, guid: 6e5dc5fe759dd8041a5b465d979a0864, type: 3}
    - {fileID: 21300000, guid: c14dab27414a1e749825d7312c0f5dcf, type: 3}
    - {fileID: 21300000, guid: 6abbbea265625934e9be2aa26b83518c, type: 3}
    - {fileID: 21300000, guid: 321a88fc5cf0459428eb1ca372127364, type: 3}
    - {fileID: 21300000, guid: 32c5fd327796d72439f1913d4cd6dd7f, type: 3}
    - {fileID: 21300000, guid: fb2fd6e505a31d1448766510d400f74c, type: 3}
    - {fileID: 21300000, guid: 6469790216861cc41bd8325eb0b9c01a, type: 3}
    - {fileID: 21300000, guid: f2280a5780ce60a45905b1025c9ca8ba, type: 3}
    - {fileID: 21300000, guid: e120fe40fce1a234b878cc0a3a194864, type: 3}
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 0.5833334
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves: []
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
