using UnityEngine;
using QFramework;
using QHLC.Views;
using QHLC.Models;
using QHLC.Events;
using QHLC.Utilities;
using QHLC.FSM.HLight;
using QHLC.Commands;
using DG.Tweening;
using Coffee.UIEffects;
using System.Collections;
using System.Collections.Generic;

namespace QHLC.FSM.Wheel
{
    /// <summary>
    /// 转盘闲置状态
    /// 包含嵌套的LightFSM
    /// </summary>
    public class WheelIdleHState : AbstractHState<WheelStateType, WheelView>
    {
        private LightView lightView;
        private LightHFSM lightFSM;

        public WheelIdleHState(HFSM<WheelStateType> fsm, WheelView wheelView, LightView lightView) : base(fsm, wheelView)
        {
            this.lightView = lightView;
        }

        protected override void OnEnter()
        {
            ModuleLogManager.LogWheel("WheelIdleHState: 进入闲置状态");

            // 获取WheelModel并重置转盘状态
            var wheelModel = mTarget.GetModel<WheelModel>();
            if (wheelModel != null)
            {
                // 重置转盘状态，确保IsSpinning和IsFlashing都为false
                wheelModel.IsSpinning.Value = false;
                wheelModel.IsFlashing.Value = false;
                wheelModel.CurrentState.Value = WheelState.Idle;
                ModuleLogManager.LogWheel("WheelIdleHState: 重置转盘模型状态 - IsSpinning: false, IsFlashing: false, CurrentState: Idle");
            }

            // 移除hasTriggeredSpinning相关逻辑，现在由GameFSMCoordinator统一处理缓存检查

            // 开始闲置旋转
            mTarget.StartIdleRotation();

            // 初始化嵌套的LightFSM
            if (lightView != null)
            {
                lightFSM = lightView.GetComponent<LightHFSM>();
                if (lightFSM != null)
                {
                    lightFSM.Initialize();
                    lightFSM.StartFSM();
                    ModuleLogManager.LogWheel("WheelIdleHState: 初始化并启动嵌套LightFSM");
                }
            }
        }

        protected override void OnExit()
        {
            ModuleLogManager.LogWheel("WheelIdleHState: 退出闲置状态");

            // 停止闲置旋转
            mTarget.StopIdleRotation();

            // 注意：不在这里停止LightFSM，让它自己管理生命周期
        }

        protected override void OnUpdate()
        {
            // 检查是否有转盘缓存次数
            var wheelBufferSystem = mTarget.GetSystem<WheelBufferSystem>();

            if (wheelBufferSystem != null && wheelBufferSystem.HasBufferedSpins())
            {
                // 检查潜艇是否处于闲置状态
                var gameController = Controllers.GameController.Instance;
                if (gameController != null && gameController.submarineFSM != null)
                {
                    var submarineFSM = gameController.submarineFSM;
                    if (submarineFSM.CurrentStateId == SubmarineStateType.Idle)
                    {
                        ModuleLogManager.LogWheel($"WheelIdleHState: 检测到转盘缓存次数({wheelBufferSystem.GetBufferedSpinCount()})且潜艇处于闲置状态，开始消耗缓存并驱动转盘旋转");

                        // 消耗转盘缓存次数并开始旋转
                        if (wheelBufferSystem.DriveWheelSpinning())
                        {
                            ModuleLogManager.LogWheel("WheelIdleHState: 成功驱动转盘旋转，通过GameFSMCoordinator请求切换到旋转状态");

                            // 通过GameFSMCoordinator请求状态切换
                            if (GameFSMCoordinator.Instance != null)
                            {
                                GameFSMCoordinator.Instance.RequestWheelStateChange(WheelStateType.Spinning);
                            }
                            else
                            {
                                ModuleLogManager.LogWheel("WheelIdleHState: GameFSMCoordinator单例未找到，直接切换状态");
                                mFSM.ChangeState(WheelStateType.Spinning);
                            }
                        }
                        else
                        {
                            ModuleLogManager.LogWheel("WheelIdleHState: 驱动转盘旋转失败");
                        }
                    }
                    else
                    {
                        ModuleLogManager.LogWheel($"WheelIdleHState: 有转盘缓存次数但潜艇不在闲置状态(当前状态: {submarineFSM.CurrentStateId})，等待潜艇闲置");
                    }
                }
                else
                {
                    ModuleLogManager.LogWheel("WheelIdleHState: 无法获取潜艇状态机引用");
                }
            }

            // 更新嵌套的LightFSM
            if (lightFSM != null)
            {
                // LightFSM有自己的Update循环，这里不需要手动调用
            }
        }

        /// <summary>
        /// 激活嵌套的LightFSM
        /// </summary>
        public void ActivateLight()
        {
            if (lightFSM != null)
            {
                ModuleLogManager.LogWheel("WheelIdleHState: 激活嵌套LightFSM，切换到Activating状态");
                lightFSM.ChangeState(LightStateType.Activating);
            }
            else
            {
                ModuleLogManager.LogWheel("WheelIdleHState: 无法激活LightFSM - lightFSM为null");
            }
        }
    }

    /// <summary>
    /// 转盘旋转状态
    /// </summary>
    public class WheelSpinningHState : AbstractHState<WheelStateType, WheelView>
    {
        private WheelModel wheelModel;

        public WheelSpinningHState(HFSM<WheelStateType> fsm, WheelView target) : base(fsm, target)
        {
            wheelModel = target.GetModel<WheelModel>();
        }

        protected override void OnEnter()
        {
            ModuleLogManager.LogWheel("WheelSpinningHState: 进入转盘旋转状态");

            // 重置标志
            wheelModel.SpinningCompleted.Value = false;

            // 开始旋转转盘
            mTarget.SendCommand(new StartWheelSpinningCommand(wheelModel.TargetResult.Value));

            // 注册转盘停止事件
            TypeEventSystem.Global.Register<WheelStoppedEvent>(OnWheelStopped);
        }

        protected override void OnUpdate()
        {
            // WheelSystem已经直接处理状态切换到闪烁，这里不需要检查SpinningCompleted
            // 保留这个方法以防将来需要添加其他逻辑
        }

        protected override void OnExit()
        {
            ModuleLogManager.LogWheel("WheelSpinningHState: 退出转盘旋转状态");

            // 重置转盘旋转状态
            if (wheelModel != null)
            {
                wheelModel.IsSpinning.Value = false;
                ModuleLogManager.LogWheel("WheelSpinningHState: 重置转盘模型旋转状态 - IsSpinning: false");
            }

            // 注销转盘停止事件
            TypeEventSystem.Global.UnRegister<WheelStoppedEvent>(OnWheelStopped);
        }

        private void OnWheelStopped(WheelStoppedEvent evt)
        {
            ModuleLogManager.LogWheel($"WheelSpinningHState: 收到转盘停止事件，结果: {evt.Result}");

            // 设置旋转完成标志
            wheelModel.SpinningCompleted.Value = true;
        }
    }

    /// <summary>
    /// 转盘闪烁状态
    /// </summary>
    public class WheelFlashingHState : AbstractHState<WheelStateType, WheelView>
    {
        private WheelModel wheelModel;
        private bool flashingCompleted = false;
        private bool hasRequestedStateChange = false; // 添加标志位防止重复调用

        public WheelFlashingHState(HFSM<WheelStateType> fsm, WheelView target) : base(fsm, target)
        {
            wheelModel = target.GetModel<WheelModel>();
        }

        protected override void OnEnter()
        {
            ModuleLogManager.LogWheel("WheelFlashingHState: 进入转盘闪烁状态");

            // 重置标志
            wheelModel.FlashingCompleted.Value = false;
            flashingCompleted = false;
            hasRequestedStateChange = false; // 重置防重复调用标志

            // 只注册闪烁完成事件，不再监听潜艇移动完成事件
            TypeEventSystem.Global.Register<WheelFlashingCompletedEvent>(OnFlashingCompleted);

            // 开始闪烁动画
            mTarget.SendCommand(new StartWheelFlashingCommand(wheelModel.TargetResult.Value));
        }

        protected override void OnUpdate()
        {
            // 检查是否需要切换到闲置状态
            // 只有在闪烁完成后，并且还没有请求过状态切换时才执行
            if (wheelModel.FlashingCompleted.Value && !hasRequestedStateChange)
            {
                // 简化逻辑：闪烁完成后直接切换到闲置状态，不等待潜艇移动完成
                // 因为潜艇移动是独立的流程，转盘不需要等待潜艇移动完成
                ModuleLogManager.LogWheel("WheelFlashingHState: 闪烁完成，通过GameFSMCoordinator请求切换到闲置状态");
                hasRequestedStateChange = true; // 设置标志位防止重复调用

                // 通过GameFSMCoordinator请求状态切换
                if (GameFSMCoordinator.Instance != null)
                {
                    GameFSMCoordinator.Instance.RequestWheelStateChange(WheelStateType.Idle);
                }
                else
                {
                    ModuleLogManager.LogWheel("WheelFlashingHState: GameFSMCoordinator单例未找到，直接切换状态");
                    mFSM.ChangeState(WheelStateType.Idle);
                }
            }
        }

        protected override void OnExit()
        {
            ModuleLogManager.LogWheel("WheelFlashingHState: 退出转盘闪烁状态");

            // 重置转盘闪烁状态
            var wheelModel = mTarget.GetModel<WheelModel>();
            if (wheelModel != null)
            {
                wheelModel.IsFlashing.Value = false;
                ModuleLogManager.LogWheel("WheelFlashingHState: 重置转盘模型闪烁状态 - IsFlashing: false");
            }

            // 注销事件
            TypeEventSystem.Global.UnRegister<WheelFlashingCompletedEvent>(OnFlashingCompleted);
        }

        private void OnFlashingCompleted(WheelFlashingCompletedEvent evt)
        {
            ModuleLogManager.LogWheel("WheelFlashingHState: 收到闪烁完成事件");
            flashingCompleted = true;

            // 直接设置闪烁完成标志，不再等待潜艇移动
            wheelModel.FlashingCompleted.Value = true;
        }
    }
}