{"DOTweenSettings": ["DOTweenSettings"], "PathKitSettings": ["PathKitSettings"], "人": ["AeImages/人"], "背景": ["AeImages/背景"], "鳥": ["AeImages/鳥"], "BuildingConfig": ["Configs/BuildingConfig"], "GameConfig": ["Configs/GameConfig"], "LightConfig": ["Configs/LightConfig"], "RewardConfig": ["Configs/RewardConfig"], "ScrollInfoSettings": ["Configs/ScrollInfoSettings"], "SubmarineConfig": ["Configs/SubmarineConfig"], "WheelConfig": ["Configs/WheelConfig"], "AeImages_Directory": ["人", "背景", "鳥"], "Configs_Directory": ["BuildingConfig", "GameConfig", "LightConfig", "RewardConfig", "ScrollInfoSettings", "SubmarineConfig", "WheelConfig"], "LineBreaking Following Characters": ["LineBreaking Following Characters"], "LineBreaking Leading Characters": ["LineBreaking Leading Characters"], "TMP Settings": ["TMP Settings"], "LiberationSans SDF - Drop Shadow": ["Fonts & Materials/LiberationSans SDF - Drop Shadow"], "LiberationSans SDF - Fallback": ["Fonts & Materials/LiberationSans SDF - Fallback"], "LiberationSans SDF - Outline": ["Fonts & Materials/LiberationSans SDF - Outline"], "LiberationSans SDF": ["Fonts & Materials/LiberationSans SDF"], "EmojiOne": ["Sprite Assets/EmojiOne"], "Default Style Sheet": ["Style Sheets/Default Style Sheet"], "Fonts & Materials_Directory": ["LiberationSans SDF - Drop Shadow", "LiberationSans SDF - Fallback", "LiberationSans SDF - Outline", "LiberationSans SDF"], "Sprite Assets_Directory": ["EmojiOne"], "Style Sheets_Directory": ["Default Style Sheet"], "AssetBundleMap": ["AssetBundleMap"], "GameVersion": ["GameVersion"], "ResourceMap": ["ResourceMap"], "Default-Transition": ["Default-Transition"], "UIDissolve": ["UIDissolve"], "UIEffect": ["UIEffect"], "UIEffecta5c739cd-187f-4ea2-8196-44fa8793e786": ["UIEffect"], "UIEffectSprite": ["UIEffectSprite"], "UIHsvModifier": ["UIHsvModifier"], "UIShiny": ["UIShiny"], "UITtransition": ["UITtransition"], "CustomBuildButtonOutlined": ["com.disillusion.play-mode-plus/CustomBuildButtonOutlined"], "CustomDropdownArrow": ["com.disillusion.play-mode-plus/CustomDropdownArrow"], "CustomPlayButton": ["com.disillusion.play-mode-plus/CustomPlayButton"], "CustomPlayStopButton": ["com.disillusion.play-mode-plus/CustomPlayStopButton"], "CustomSceneCogButton": ["com.disillusion.play-mode-plus/CustomSceneCogButton"], "PlayModePlusToolbar": ["com.disillusion.play-mode-plus/PlayModePlusToolbar"], "PlayModePlusToolbar6000": ["com.disillusion.play-mode-plus/PlayModePlusToolbar6000"], "com.disillusion_Directory": ["CustomBuildButtonOutlined", "CustomDropdownArrow", "CustomPlayButton", "CustomPlayStopButton", "CustomSceneCogButton", "PlayModePlusToolbar", "PlayModePlusToolbar6000"], "GlobalSerializationConfig": ["Sirenix/GlobalSerializationConfig"], "Sirenix_Directory": ["GlobalSerializationConfig"], "UIRoot": ["UIRoot"], "QFramework_Directory": [], "PathKit_Directory": [], "Prefabs_Directory": [], "Sprites_Directory": [], "ScreenTransitionCanvas": ["ScreenTransitionCanvas"], "PositionMarkForLoad": ["EditorGuideline/PositionMarkForLoad"], "01.简介": ["EditorGuideline/1. 介绍/01.简介"], "02.Roadmap-路线图": ["EditorGuideline/1. 介绍/02.Roadmap-路线图"], "01. QFramework 架构简介": ["EditorGuideline/2. 架构篇：QFramework.cs/01. QFramework 架构简介"], "02. 基本的 MVC": ["EditorGuideline/2. 架构篇：QFramework.cs/02. 基本的 MVC"], "03. 引入 Command": ["EditorGuideline/2. 架构篇：QFramework.cs/03. 引入 Command"], "04. 引入 Event": ["EditorGuideline/2. 架构篇：QFramework.cs/04. 引入 Event"], "05. 引入 Utility": ["EditorGuideline/2. 架构篇：QFramework.cs/05. 引入 Utility"], "06. 引入 System": ["EditorGuideline/2. 架构篇：QFramework.cs/06. 引入 System"], "07. 使用 BindableProperty 优化事件": ["EditorGuideline/2. 架构篇：QFramework.cs/07. 使用 BindableProperty 优化事件"], "08. 用接口设计模块（依赖倒置原则）": ["EditorGuideline/2. 架构篇：QFramework.cs/08. 用接口设计模块（依赖倒置原则）"], "09. Query 介绍": ["EditorGuideline/2. 架构篇：QFramework.cs/09. Query 介绍"], "10. 架构规范 与 推荐用法": ["EditorGuideline/2. 架构篇：QFramework.cs/10. 架构规范 与 推荐用法"], "11. 光速实现 EditorCounterApp 和 给主程看的开发模式": ["EditorGuideline/2. 架构篇：QFramework.cs/11. 光速实现 EditorCounterApp 和 给主程看的开发模式"], "12. 纸上设计": ["EditorGuideline/2. 架构篇：QFramework.cs/12. 纸上设计"], "13. Architecture 的好处": ["EditorGuideline/2. 架构篇：QFramework.cs/13. Architecture 的好处"], "14. Command 拦截": ["EditorGuideline/2. 架构篇：QFramework.cs/14. Command 拦截"], "15. 内置工具：TypeEventSystem": ["EditorGuideline/2. 架构篇：QFramework.cs/15. 内置工具：TypeEventSystem"], "16. 内置工具：EasyEvent": ["EditorGuideline/2. 架构篇：QFramework.cs/16. 内置工具：EasyEvent"], "17. 内置工具：BindableProperty": ["EditorGuideline/2. 架构篇：QFramework.cs/17. 内置工具：BindableProperty"], "18. 内置工具：IOCContainer": ["EditorGuideline/2. 架构篇：QFramework.cs/18. 内置工具：IOCContainer"], "19. 心中有架构": ["EditorGuideline/2. 架构篇：QFramework.cs/19. 心中有架构"], "20. QFramework.cs 的更多内容": ["EditorGuideline/2. 架构篇：QFramework.cs/20. QFramework.cs 的更多内容"], "01. QFramework.Toolkits 简介": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/01. QFramework.Toolkits 简介"], "02. 下载与安装": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/02. 下载与安装"], "08. FluentKit 链式 API ": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/08. FluentKit 链式 API "], "10. FSMKit 状态机": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/10. FSMKit 状态机"], "11. PoolKit 对象池套件": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/11. PoolKit 对象池套件"], "12. TableKit 表数据结构": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/12. TableKit 表数据结构"], "13. 其他事件工具": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/13. 其他事件工具"], "14. 更多内容": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/14. 更多内容"], "15. 补充内容：GridKit 二维格子数据结构": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/15. 补充内容：GridKit 二维格子数据结构"], "16. 补充内容：LiveCodingKit 写代码不用停止运行的利器": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/16. 补充内容：LiveCodingKit 写代码不用停止运行的利器"], "17. 补充内容：动态二维格子数据结构 DynaGrid（GridKit）": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/17. 补充内容：动态二维格子数据结构 DynaGrid（GridKit）"], "20240918. 新增 BindableList": ["EditorGuideline/5. v1.0 之后新增功能/20240918. 新增 BindableList"], "20240919. 新增 BindableDictionary": ["EditorGuideline/5. v1.0 之后新增功能/20240919. 新增 BindableDictionary"], "20241016. 新增 ScreenTransition": ["EditorGuideline/5. v1.0 之后新增功能/20241016. 新增 ScreenTransition"], "20241108. 受 Unity Bug 影响的已知问题1": ["EditorGuideline/5. v1.0 之后新增功能/20241108. 受 Unity Bug 影响的已知问题1"], "01. 基本使用": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/03. CodeGenKit 脚本生成/01. 基本使用"], "02. 增量生成": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/03. CodeGenKit 脚本生成/02. 增量生成"], "03. 类型选择": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/03. CodeGenKit 脚本生成/03. 类型选择"], "04. ViewController 嵌套": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/03. CodeGenKit 脚本生成/04. ViewController 嵌套"], "05. 设置命名空间和生成目录": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/03. CodeGenKit 脚本生成/05. 设置命名空间和生成目录"], "06. 生成 Prefab": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/03. CodeGenKit 脚本生成/06. 生成 Prefab"], "07. 如何设置 ViewController 的父类": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/03. CodeGenKit 脚本生成/07. 如何设置 ViewController 的父类"], "08. OtherBinds": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/03. CodeGenKit 脚本生成/08. OtherBinds"], "09. why": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/03. CodeGenKit 脚本生成/09. why"], "01. 简介": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/04. ActionKit 时序动作执行系统/01. 简介"], "02. 延时 Delay": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/04. ActionKit 时序动作执行系统/02. 延时 Delay"], "03. 序列 Sequence": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/04. ActionKit 时序动作执行系统/03. 序列 Sequence"], "04. 帧延时 DelayFrame": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/04. ActionKit 时序动作执行系统/04. 帧延时 DelayFrame"], "05. 条件 Condition": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/04. ActionKit 时序动作执行系统/05. 条件 Condition"], "06. 重复 Repeat": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/04. ActionKit 时序动作执行系统/06. 重复 Repeat"], "07. 并行 Parallel": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/04. ActionKit 时序动作执行系统/07. 并行 Parallel"], "08. 复杂示例": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/04. ActionKit 时序动作执行系统/08. 复杂示例"], "09. 自定义 Custom": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/04. ActionKit 时序动作执行系统/09. 自定义 Custom"], "10. 协程 Coroutine": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/04. ActionKit 时序动作执行系统/10. 协程 Coroutine"], "11. Mono 生民周期事件": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/04. ActionKit 时序动作执行系统/11. Mono 生民周期事件"], "12. DOTween 集成": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/04. ActionKit 时序动作执行系统/12. DOTween 集成"], "13. UniRx 集成": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/04. ActionKit 时序动作执行系统/13. UniRx 集成"], "14. 切换场景时停止": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/04. ActionKit 时序动作执行系统/14. 切换场景时停止"], "15. 不受 TimeScale 限制执行动作": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/04. ActionKit 时序动作执行系统/15. 不受 TimeScale 限制执行动作"], "01. 简介与快速开始": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/05. SingletonKit 单例套件/01. 简介与快速开始"], "02. C# 单例": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/05. SingletonKit 单例套件/02. C# 单例"], "03. Mono 单例": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/05. SingletonKit 单例套件/03. Mono 单例"], "04. Mono 属性单例": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/05. SingletonKit 单例套件/04. Mono 属性单例"], "05. C# 属性单例": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/05. SingletonKit 单例套件/05. C# 属性单例"], "06. 单例自动命名": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/05. SingletonKit 单例套件/06. 单例自动命名"], "07. 持久化单例": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/05. SingletonKit 单例套件/07. 持久化单例"], "08. 可替换单例": ["EditorGuideline/3. 工具篇：QFramework.Toolkits/05. SingletonKit 单例套件/08. 可替换单例"], "01. 简介与快速入门": ["EditorGuideline/4. 解决方案篇/01. ResKit：资源管理&开发解决方案/01. 简介与快速入门"], "02. 模拟模式与真机模式": ["EditorGuideline/4. 解决方案篇/01. ResKit：资源管理&开发解决方案/02. 模拟模式与真机模式"], "03. 异步加载": ["EditorGuideline/4. 解决方案篇/01. ResKit：资源管理&开发解决方案/03. 异步加载"], "04. 场景加载": ["EditorGuideline/4. 解决方案篇/01. ResKit：资源管理&开发解决方案/04. 场景加载"], "05. 从 Resources 加载资源": ["EditorGuideline/4. 解决方案篇/01. ResKit：资源管理&开发解决方案/05. 从 Resources 加载资源"], "06. 与资源关联的对象管理": ["EditorGuideline/4. 解决方案篇/01. ResKit：资源管理&开发解决方案/06. 与资源关联的对象管理"], "07. SpriteAtlas 加载": ["EditorGuideline/4. 解决方案篇/01. ResKit：资源管理&开发解决方案/07. SpriteAtlas 加载"], "08. 加载网络图片": ["EditorGuideline/4. 解决方案篇/01. ResKit：资源管理&开发解决方案/08. 加载网络图片"], "09. 加载 PersitentDataPath 图片": ["EditorGuideline/4. 解决方案篇/01. ResKit：资源管理&开发解决方案/09. 加载 PersitentDataPath 图片"], "10. 自定义 Res": ["EditorGuideline/4. 解决方案篇/01. ResKit：资源管理&开发解决方案/10. 自定义 Res"], "11. 代码生成": ["EditorGuideline/4. 解决方案篇/01. ResKit：资源管理&开发解决方案/11. 代码生成"], "12. ResLoader 推荐用法": ["EditorGuideline/4. 解决方案篇/01. ResKit：资源管理&开发解决方案/12. ResLoader 推荐用法"], "13. WebGL 注意事项": ["EditorGuideline/4. 解决方案篇/01. ResKit：资源管理&开发解决方案/13. WebGL 注意事项"], "01. 简介与快速入门cf4e9fda-98c8-4062-967f-3918c8a0946e": ["EditorGuideline/4. 解决方案篇/02. UIKit：界面管理&快速开发解决方案/01. 简介与快速入门"], "02. 打开、关闭界面": ["EditorGuideline/4. 解决方案篇/02. UIKit：界面管理&快速开发解决方案/02. 打开、关闭界面"], "03. 异步加载界面": ["EditorGuideline/4. 解决方案篇/02. UIKit：界面管理&快速开发解决方案/03. 异步加载界面"], "04. 生命周期": ["EditorGuideline/4. 解决方案篇/02. UIKit：界面管理&快速开发解决方案/04. 生命周期"], "05. 剩下的 API": ["EditorGuideline/4. 解决方案篇/02. UIKit：界面管理&快速开发解决方案/05. 剩下的 API"], "06. Panel 自动生成工具": ["EditorGuideline/4. 解决方案篇/02. UIKit：界面管理&快速开发解决方案/06. Panel 自动生成工具"], "07. UIElement 简介": ["EditorGuideline/4. 解决方案篇/02. UIKit：界面管理&快速开发解决方案/07. UIElement 简介"], "08. 同一类型的界面打开多个": ["EditorGuideline/4. 解决方案篇/02. UIKit：界面管理&快速开发解决方案/08. 同一类型的界面打开多个"], "09. 自定义加载界面": ["EditorGuideline/4. 解决方案篇/02. UIKit：界面管理&快速开发解决方案/09. 自定义加载界面"], "10. 小结": ["EditorGuideline/4. 解决方案篇/02. UIKit：界面管理&快速开发解决方案/10. 小结"], "01. 快速入门": ["EditorGuideline/4. 解决方案篇/03. AudioKit 音频管理解决方案/01. 快速入门"], "02. 自定义加载": ["EditorGuideline/4. 解决方案篇/03. AudioKit 音频管理解决方案/02. 自定义加载"], "03. ActionKit 支持": ["EditorGuideline/4. 解决方案篇/03. AudioKit 音频管理解决方案/03. ActionKit 支持"], "04. 链式 API 支持": ["EditorGuideline/4. 解决方案篇/03. AudioKit 音频管理解决方案/04. 链式 API 支持"], "EditorGuideline_Directory": ["PositionMarkForLoad"], "1_Directory": ["01.简介", "02.Roadmap-路线图"], "2. 架构篇：QFramework_Directory": ["01. QFramework 架构简介", "02. 基本的 MVC", "03. 引入 Command", "04. 引入 Event", "05. 引入 Utility", "06. 引入 System", "07. 使用 BindableProperty 优化事件", "08. 用接口设计模块（依赖倒置原则）", "09. Query 介绍", "10. 架构规范 与 推荐用法", "11. 光速实现 EditorCounterApp 和 给主程看的开发模式", "12. 纸上设计", "13. Architecture 的好处", "14. Command 拦截", "15. 内置工具：TypeEventSystem", "16. 内置工具：EasyEvent", "17. 内置工具：BindableProperty", "18. 内置工具：IOCContainer", "19. 心中有架构", "20. QFramework.cs 的更多内容"], "3. 工具篇：QFramework_Directory": ["01. QFramework.Toolkits 简介", "02. 下载与安装", "08. FluentKit 链式 API ", "10. FSMKit 状态机", "11. PoolKit 对象池套件", "12. TableKit 表数据结构", "13. 其他事件工具", "14. 更多内容", "15. 补充内容：GridKit 二维格子数据结构", "16. 补充内容：LiveCodingKit 写代码不用停止运行的利器", "17. 补充内容：动态二维格子数据结构 DynaGrid（GridKit）"], "4_Directory": [], "5. v1_Directory": ["20240918. 新增 BindableList", "20240919. 新增 BindableDictionary", "20241016. 新增 ScreenTransition", "20241108. 受 Unity Bug 影响的已知问题1"], "03_Directory": ["01. 基本使用", "02. 增量生成", "03. 类型选择", "04. ViewController 嵌套", "05. 设置命名空间和生成目录", "06. 生成 Prefab", "07. 如何设置 ViewController 的父类", "08. <PERSON><PERSON><PERSON><PERSON>", "09. why"], "04_Directory": ["01. 简介", "02. 延时 Delay", "03. 序列 Sequence", "04. 帧延时 DelayFrame", "05. 条件 Condition", "06. 重复 Repeat", "07. 并行 <PERSON><PERSON><PERSON>", "08. 复杂示例", "09. 自定义 Custom", "10. 协程 Coroutine", "11. <PERSON><PERSON> 生民周期事件", "12. <PERSON><PERSON><PERSON><PERSON> 集成", "13. UniRx 集成", "14. 切换场景时停止", "15. 不受 TimeScale 限制执行动作"], "05_Directory": ["01. 简介与快速开始", "02. C# 单例", "03. <PERSON><PERSON> 单例", "04. Mono 属性单例", "05. C# 属性单例", "06. 单例自动命名", "07. 持久化单例", "08. 可替换单例"], "01_Directory": ["01. 简介与快速入门", "02. 模拟模式与真机模式", "03. 异步加载", "04. 场景加载", "05. 从 Resources 加载资源", "06. 与资源关联的对象管理", "07. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 加载", "08. 加载网络图片", "09. 加载 PersitentDataPath 图片", "10. 自定义 Res", "11. 代码生成", "12. <PERSON><PERSON><PERSON><PERSON><PERSON> 推荐用法", "13. WebGL 注意事项"], "02_Directory": ["01. 简介与快速入门", "02. 打开、关闭界面", "03. 异步加载界面", "04. 生命周期", "05. 剩下的 API", "06. Panel 自动生成工具", "07. <PERSON><PERSON><PERSON> 简介", "08. 同一类型的界面打开多个", "09. 自定义加载界面", "10. 小结"], "03_Directoryef56fd6e-f0b5-49a3-8dcf-cdfa8b201510": ["01. 快速入门", "02. 自定义加载", "03. ActionKit 支持", "04. 链式 API 支持"], "background_qs": ["Skin/background_qs"], "black": ["Skin/black"], "blockcode-ds": ["Skin/blockcode-ds"], "blockcode": ["Skin/blockcode"], "blockquote-ds": ["Skin/blockquote-ds"], "blockquote": ["Skin/blockquote"], "border": ["Skin/border"], "btn_back": ["Skin/btn_back"], "btn_file": ["Skin/btn_file"], "btn_forward": ["Skin/btn_forward"], "btn_placeholder": ["Skin/btn_placeholder"], "btn_raw": ["Skin/btn_raw"], "highlight": ["Skin/highlight"], "MarkdownSkinQS": ["Skin/MarkdownSkinQS"], "MarkdownViewerSkin": ["Skin/MarkdownViewerSkin"], "SourceCodePro-Light": ["Skin/SourceCodePro-Light"], "SourceCodePro-Regular": ["Skin/SourceCodePro-Regular"], "SourceSansPro-Light": ["Skin/SourceSansPro-Light"], "th-ds": ["Skin/th-ds"], "th": ["Skin/th"], "tr-ds": ["Skin/tr-ds"], "tr": ["Skin/tr"], "trl-ds": ["Skin/trl-ds"], "trl": ["Skin/trl"], "white": ["Skin/white"], "Skin_Directory": ["background_qs", "black", "blockcode-ds", "blockcode", "blockquote-ds", "blockquote", "border", "btn_back", "btn_file", "btn_forward", "btn_placeholder", "btn_raw", "highlight", "MarkdownSkinQS", "<PERSON><PERSON><PERSON>iewer<PERSON><PERSON>", "SourceCodePro-Light", "SourceCodePro-Regular", "SourceSansPro-Light", "th-ds", "th", "tr-ds", "tr", "trl-ds", "trl", "white"], "graphkit_imgui_dot": ["graphkit_imgui_dot"], "graphkit_imgui_dot_outer": ["graphkit_imgui_dot_outer"], "graphkit_imgui_node": ["graphkit_imgui_node"], "graphkit_imgui_node_highlight": ["graphkit_imgui_node_highlight"], "graphKit_imgui_node_workfile": ["graphKit_imgui_node_workfile"], "IMGUIGraphNodeTemplate.cs": ["ScriptTemplates/IMGUIGraphNodeTemplate.cs"], "IMGUIGraphTemplate.cs": ["ScriptTemplates/IMGUIGraphTemplate.cs"], "ScriptTemplates_Directory": ["IMGUIGraphNodeTemplate.cs", "IMGUIGraphTemplate.cs"]}