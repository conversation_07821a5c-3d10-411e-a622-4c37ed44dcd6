using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using QFramework;
using DG.Tweening;
using QHLC.Models;
using QHLC.Events;
using QHLC.Utilities;
using QHLC.Common;
using QHLC.FSM;
using QHLC.Views;
using QHLC.Controllers;
using F8Framework.Core;
using System.Linq;
using QHLC;

namespace QHLC.Managers
{
    /// <summary>
    /// 潜艇管理器单例
    /// 简化潜艇移动调用链，集中管理所有潜艇相关功能
    /// </summary>
    public class SubmarineManager : MonoBehaviour, IController
    {
        #region 单例实现
        private static SubmarineManager _instance;
        public static SubmarineManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType<SubmarineManager>();
                    if (_instance == null)
                    {
                        GameObject go = new GameObject("SubmarineManager");
                        _instance = go.AddComponent<SubmarineManager>();
                        DontDestroyOnLoad(go);
                    }
                }
                return _instance;
            }
        }
        #endregion

        #region 组件引用
        [Header("潜艇组件")]
        [SerializeField] private RectTransform submarineRectTransform;
        [SerializeField] private Image submarineImage;
        [SerializeField] private Sprite[] directionSprites;
        [SerializeField] private PathItem[] pathItems; // 🔧 路径点数组 - 必须在Inspector中手动配置
        [SerializeField] private PathKitSettings pathKitSettings; // 🔧 PathKit配置 - 仅用于移动参数和方向精灵，不使用其pathPoints字段
        [SerializeField] private FSM.SubmarineFSM submarineFSM;

        private SubmarinePathController pathController;
        private ConfigurationService configService;
        private bool isMoving = false;
        private int currentStationIndex = 0;
        private bool isAddedToPath = false; // 🔧 新增：防止重复添加到路径的标志

        // 🔧 新增：防重复初始化标志
        private static bool isSystemInitialized = false;
        #endregion

        #region 初始化
        private void Awake()
        {
            // 确保单例唯一性
            if (_instance != null && _instance != this)
            {
                Destroy(gameObject);
                return;
            }
            _instance = this;
            DontDestroyOnLoad(gameObject);

            // 初始化服务
            configService = this.GetUtility<ConfigurationService>();

            ModuleLogManager.LogSubmarine("SubmarineManager.Awake: 单例初始化完成");
        }

        private void Start()
        {
            InitializeSubmarine();
        }

        /// <summary>
        /// 重置初始化状态（用于调试和重新初始化）
        /// </summary>
        public static void ResetInitializationState()
        {
            isSystemInitialized = false;
            ModuleLogManager.LogSubmarine("SubmarineManager.ResetInitializationState: 已重置初始化状态，允许重新初始化");
        }

        /// <summary>
        /// 检查系统是否已初始化
        /// </summary>
        public static bool IsSystemInitialized()
        {
            return isSystemInitialized;
        }

        /// <summary>
        /// 初始化潜艇系统
        /// </summary>
        public void InitializeSubmarine()
        {
            // 🔧 防重复初始化检查
            if (isSystemInitialized)
            {
                ModuleLogManager.LogSubmarine("SubmarineManager.InitializeSubmarine: 系统已初始化，跳过重复初始化");
                return;
            }

            ModuleLogManager.LogSubmarine("SubmarineManager.InitializeSubmarine: 开始初始化（唯一入口）");

            try
            {
                // 自动查找潜艇组件（如果未在Inspector中设置）
                FindSubmarineComponents();

                // 初始化路径点
                InitializePathItems();

                // 加载PathKit设置
                LoadPathKitSettings();

                // 初始化PathController
                InitializePathController();

                // 重置潜艇到初始位置
                ResetSubmarinePosition();

                // 🔧 新增：将潜艇添加到PathManager路径系统中
                // 注意：这提供了与UIRectTransformPathExample相同的路径管理功能
                AddSubmarineToPath();

                // 🔧 新增：验证所有字段赋值状态
                ValidateAllFields();

                // 🔧 标记系统已初始化
                isSystemInitialized = true;

                ModuleLogManager.LogSubmarine("SubmarineManager.InitializeSubmarine: 初始化完成（已设置防重复标志）");
            }
            catch (System.Exception ex)
            {
                ModuleLogManager.LogSubmarineError($"SubmarineManager.InitializeSubmarine: 初始化失败 - {ex.Message}");
                // 初始化失败时重置标志，允许重试
                isSystemInitialized = false;
            }
        }

        /// <summary>
        /// 验证潜艇组件（不再自动查找）
        /// </summary>
        private void FindSubmarineComponents()
        {
            ModuleLogManager.LogSubmarine("SubmarineManager.FindSubmarineComponents: 开始验证组件配置");

            // 🔧 简化：不再自动查找，要求在Inspector中手动配置
            bool hasErrors = false;

            // 验证必要组件是否在Inspector中配置
            if (submarineRectTransform == null)
            {
                ModuleLogManager.LogSubmarineError("SubmarineManager.FindSubmarineComponents: submarineRectTransform未在Inspector中配置！");
                hasErrors = true;
            }
            else
            {
                ModuleLogManager.LogSubmarine($"SubmarineManager.FindSubmarineComponents: submarineRectTransform验证通过: {submarineRectTransform.name}");
            }

            if (submarineImage == null)
            {
                ModuleLogManager.LogSubmarineError("SubmarineManager.FindSubmarineComponents: submarineImage未在Inspector中配置！");
                hasErrors = true;
            }
            else
            {
                ModuleLogManager.LogSubmarine("SubmarineManager.FindSubmarineComponents: submarineImage验证通过");
            }

            if (pathItems == null || pathItems.Length == 0)
            {
                ModuleLogManager.LogSubmarineError("SubmarineManager.FindSubmarineComponents: pathItems未在Inspector中配置！");
                hasErrors = true;
            }
            else
            {
                ModuleLogManager.LogSubmarine($"SubmarineManager.FindSubmarineComponents: pathItems验证通过，数量: {pathItems.Length}");
            }

            if (pathKitSettings == null)
            {
                ModuleLogManager.LogSubmarineError("SubmarineManager.FindSubmarineComponents: pathKitSettings未在Inspector中配置！");
                hasErrors = true;
            }
            else
            {
                ModuleLogManager.LogSubmarine("SubmarineManager.FindSubmarineComponents: pathKitSettings验证通过");
            }

            // submarineFSM是可选的
            if (submarineFSM == null)
            {
                ModuleLogManager.LogSubmarineWarning("SubmarineManager.FindSubmarineComponents: submarineFSM未配置，状态管理功能将不可用");
            }

            // 如果有错误，抛出异常终止初始化
            if (hasErrors)
            {
                string errorMessage = "SubmarineManager组件配置不完整！请在Inspector中手动配置所有必要的组件字段。";
                ModuleLogManager.LogSubmarineError($"SubmarineManager.FindSubmarineComponents: {errorMessage}");
                throw new System.Exception(errorMessage);
            }

            // 记录验证结果
            ModuleLogManager.LogSubmarine($"SubmarineManager.FindSubmarineComponents: 组件验证通过");
            ModuleLogManager.LogSubmarine($"  - RectTransform: ✓ {submarineRectTransform.name}");
            ModuleLogManager.LogSubmarine($"  - Image: ✓");
            ModuleLogManager.LogSubmarine($"  - PathItems: ✓ ({pathItems.Length}个)");
            ModuleLogManager.LogSubmarine($"  - PathKitSettings: ✓");
            ModuleLogManager.LogSubmarine($"  - FSM: {(submarineFSM != null ? "✓" : "✗")} {(submarineFSM != null ? submarineFSM.name : "未配置")}");
        }

        /// <summary>
        /// 初始化路径点（不再自动查找）
        /// </summary>
        private void InitializePathItems()
        {
            ModuleLogManager.LogSubmarine("SubmarineManager.InitializePathItems: 开始验证路径点配置");

            // 🔧 简化：pathItems必须在Inspector中手动配置
            if (pathItems == null || pathItems.Length == 0)
            {
                string errorMessage = "pathItems未在Inspector中配置！请手动拖拽路径点到pathItems数组中。";
                ModuleLogManager.LogSubmarineError($"SubmarineManager.InitializePathItems: {errorMessage}");
                throw new System.Exception(errorMessage);
            }

            ModuleLogManager.LogSubmarine($"SubmarineManager.InitializePathItems: pathItems数组验证通过，开始验证每个路径点，总数: {pathItems.Length}");

            // 验证路径点的有效性
            var validPathItems = new List<PathItem>();
            int invalidCount = 0;

            for (int i = 0; i < pathItems.Length; i++)
            {
                var item = pathItems[i];
                if (item == null || item.transform == null)
                {
                    ModuleLogManager.LogSubmarineError($"SubmarineManager.InitializePathItems: 路径点 {i} 无效：item={item}, transform={item?.transform}");
                    invalidCount++;
                    continue;
                }

                // PathItem应该继承自BasePathItem，检查RectTransform
                if (!(item.transform is RectTransform))
                {
                    ModuleLogManager.LogSubmarineError($"SubmarineManager.InitializePathItems: 路径点 {i} ({item.name}) 不是RectTransform类型");
                    invalidCount++;
                    continue;
                }

                validPathItems.Add(item);
            }

            if (invalidCount > 0)
            {
                string errorMessage = $"发现 {invalidCount} 个无效的路径点！请检查Inspector中pathItems数组的配置。";
                ModuleLogManager.LogSubmarineError($"SubmarineManager.InitializePathItems: {errorMessage}");
                throw new System.Exception(errorMessage);
            }

            if (validPathItems.Count < 2)
            {
                string errorMessage = $"有效路径点不足2个！当前有效数量:{validPathItems.Count}，请在Inspector中配置至少2个有效的路径点。";
                ModuleLogManager.LogSubmarineError($"SubmarineManager.InitializePathItems: {errorMessage}");
                throw new System.Exception(errorMessage);
            }

            // 更新pathItems为有效的路径点
            pathItems = validPathItems.ToArray();

            // 按名称或位置排序路径点（确保顺序正确）
            System.Array.Sort(pathItems, (a, b) => string.Compare(a.name, b.name));

            // 通过QFramework发送路径点初始化命令
            this.SendCommand(new InitializePathItemsCommand(pathItems.ToList()));

            ModuleLogManager.LogSubmarine($"SubmarineManager.InitializePathItems: 路径点验证完成，有效数量: {pathItems.Length}");
        }

        /// <summary>
        /// 加载PathKit设置（不再自动创建）
        /// </summary>
        private void LoadPathKitSettings()
        {
            // 🔧 简化：pathKitSettings必须在Inspector中手动配置
            if (pathKitSettings == null)
            {
                string errorMessage = "pathKitSettings未在Inspector中配置！请手动拖拽PathKitSettings资源到该字段。";
                ModuleLogManager.LogSubmarineError($"SubmarineManager.LoadPathKitSettings: {errorMessage}");
                throw new System.Exception(errorMessage);
            }

            ModuleLogManager.LogSubmarine("SubmarineManager.LoadPathKitSettings: PathKitSettings配置验证通过");

            // 🔧 从PathKitSettings加载方向精灵数据
            LoadDirectionSpritesFromSettings();
        }

        /// <summary>
        /// 从PathKitSettings加载方向精灵
        /// 注意：这里只使用PathKitSettings的方向精灵配置，不使用其pathPoints字段
        /// 路径点数据完全来自Inspector中配置的pathItems数组
        /// </summary>
        private void LoadDirectionSpritesFromSettings()
        {
            if (pathKitSettings == null)
            {
                ModuleLogManager.LogSubmarineWarning("SubmarineManager.LoadDirectionSpritesFromSettings: pathKitSettings为空，无法加载方向精灵");
                return;
            }

            ModuleLogManager.LogSubmarine("SubmarineManager.LoadDirectionSpritesFromSettings: 开始从PathKitSettings加载方向精灵（不使用其pathPoints）");

            // 🔧 重要：使用PathKitSettings中的精灵数据替代本地directionSprites字段
            if (pathKitSettings.directionType == PathKitSettings.DirectionType.DiagonalDirections)
            {
                // 使用对角线方向精灵（与SubmarineDirection枚举对应）
                directionSprites = new Sprite[4]
                {
                    pathKitSettings.topLeftDirectionSprite,     // LeftUp
                    pathKitSettings.bottomLeftDirectionSprite,  // LeftDown
                    pathKitSettings.bottomRightDirectionSprite, // RightDown
                    pathKitSettings.topRightDirectionSprite     // RightUp
                };

                ModuleLogManager.LogSubmarine("SubmarineManager.LoadDirectionSpritesFromSettings: 使用PathKitSettings对角线方向精灵");
            }
            else
            {
                // 使用基本方向精灵（需要映射到对角线方向）
                directionSprites = new Sprite[4]
                {
                    pathKitSettings.leftDirectionSprite,   // LeftUp -> Left
                    pathKitSettings.leftDirectionSprite,   // LeftDown -> Left
                    pathKitSettings.rightDirectionSprite,  // RightDown -> Right
                    pathKitSettings.rightDirectionSprite   // RightUp -> Right
                };

                ModuleLogManager.LogSubmarine("SubmarineManager.LoadDirectionSpritesFromSettings: 使用PathKitSettings基本方向精灵（映射到对角线）");
            }

            // 检查精灵是否成功加载
            int loadedCount = 0;
            for (int i = 0; i < directionSprites.Length; i++)
            {
                if (directionSprites[i] != null)
                {
                    loadedCount++;
                }
                else
                {
                    ModuleLogManager.LogSubmarineWarning($"SubmarineManager.LoadDirectionSpritesFromSettings: 方向精灵 {i} ({(SubmarineDirection)i}) 为空");
                }
            }

            ModuleLogManager.LogSubmarine($"SubmarineManager.LoadDirectionSpritesFromSettings: 成功加载 {loadedCount}/{directionSprites.Length} 个方向精灵");

            // 🔧 验证：如果没有加载到有效精灵，创建默认精灵或警告
            if (loadedCount == 0)
            {
                ModuleLogManager.LogSubmarineWarning("SubmarineManager.LoadDirectionSpritesFromSettings: 没有加载到任何有效的方向精灵，方向更新功能将不可用");
            }
        }

        /// <summary>
        /// 初始化PathController
        /// </summary>
        private void InitializePathController()
        {
            ModuleLogManager.LogSubmarine("SubmarineManager.InitializePathController: 开始初始化PathController");

            // 验证必要组件
            if (pathItems == null || pathItems.Length == 0)
            {
                ModuleLogManager.LogSubmarineError("SubmarineManager.InitializePathController: pathItems为空或无效");
                return;
            }

            if (pathKitSettings == null)
            {
                ModuleLogManager.LogSubmarineError("SubmarineManager.InitializePathController: pathKitSettings为空");
                return;
            }

            if (gameObject == null)
            {
                ModuleLogManager.LogSubmarineError("SubmarineManager.InitializePathController: gameObject为空");
                return;
            }

            try
            {
                // 确保PathKit已初始化
                PathKitCore.InitOnLoad();

                // 🔧 重要说明：只使用编辑器配置的pathItems，不使用PathKitSettings.pathPoints
                // pathItems来源：Inspector中手动配置的PathItem数组
                // PathKitSettings仅用于移动参数（持续时间、缓动等）和方向精灵配置
                ModuleLogManager.LogSubmarine($"SubmarineManager.InitializePathController: 使用编辑器配置的pathItems，数量: {pathItems.Length}");
                ModuleLogManager.LogSubmarine("SubmarineManager.InitializePathController: 注意：不使用PathKitSettings.pathPoints，仅使用Inspector中配置的pathItems");

                // 🔧 注意：SubmarinePathController现在只负责数据管理，不自动添加到PathManager
                // 路径添加将由SubmarineManager.AddSubmarineToPath()统一处理
                ModuleLogManager.LogSubmarine("SubmarineManager.InitializePathController: 创建PathController但禁用其自动路径添加功能");

                pathController = new SubmarinePathController(
                    gameObject,
                    pathItems.ToList(), // 🔧 明确使用编辑器配置的pathItems
                    pathKitSettings     // 仅用于配置参数，不使用其中的pathPoints
                );

                if (pathController != null)
                {
                    ModuleLogManager.LogSubmarine("SubmarineManager.InitializePathController: PathController初始化成功");
                    ModuleLogManager.LogSubmarine("SubmarineManager.InitializePathController: 已将编辑器配置的pathItems传递给PathController");
                }
                else
                {
                    ModuleLogManager.LogSubmarineError("SubmarineManager.InitializePathController: PathController创建失败");
                }
            }
            catch (System.Exception ex)
            {
                ModuleLogManager.LogSubmarineError($"SubmarineManager.InitializePathController: PathController初始化异常: {ex.Message}");
                pathController = null;
            }
        }

        /// <summary>
        /// 将潜艇添加到路径（参考UIRectTransformPathExample.AddUIObjectToPath）
        /// </summary>
        private void AddSubmarineToPath()
        {
            ModuleLogManager.LogSubmarine("SubmarineManager.AddSubmarineToPath: 开始将潜艇添加到路径");

            // 🔧 防止重复添加到路径
            if (isAddedToPath)
            {
                ModuleLogManager.LogSubmarineWarning("SubmarineManager.AddSubmarineToPath: 潜艇已添加到路径，跳过重复操作");
                return;
            }

            try
            {
                if (submarineRectTransform == null)
                {
                    ModuleLogManager.LogSubmarineError("SubmarineManager.AddSubmarineToPath: 潜艇RectTransform对象不能为空!");
                    return;
                }

                // 在转换前再次检查路径点
                if (pathItems == null || pathItems.Length < 2)
                {
                    ModuleLogManager.LogSubmarineError("SubmarineManager.AddSubmarineToPath: 路径点不足或未分配，无法创建路径!");
                    ModuleLogManager.LogSubmarineError($"SubmarineManager.AddSubmarineToPath: pathItems={pathItems}, pathItems.Length={pathItems?.Length}");
                    return;
                }

                ModuleLogManager.LogSubmarine($"SubmarineManager.AddSubmarineToPath: 验证通过，路径点数量: {pathItems.Length}");

                // 获取PathManager
                var pathManager = PathKitCore.Interface.GetSystem<IPathManager>();
                if (pathManager == null)
                {
                    ModuleLogManager.LogSubmarineError("SubmarineManager.AddSubmarineToPath: 无法获取PathManager");
                    return;
                }

                // 如果潜艇已经在路径中，先移除它
                if (pathManager.HasPath(gameObject))
                {
                    ModuleLogManager.LogSubmarine("SubmarineManager.AddSubmarineToPath: 潜艇已在路径中，先移除");
                    pathManager.RemoveFromPath(gameObject);
                }

                // 🔧 重要：使用编辑器配置的pathItems，不使用PathKitSettings.pathPoints
                ModuleLogManager.LogSubmarine("SubmarineManager.AddSubmarineToPath: 使用编辑器配置的pathItems创建路径");

                // 使用PathManager的具体实现
                if (pathManager is PathManager realManager)
                {
                    ModuleLogManager.LogSubmarine("SubmarineManager.AddSubmarineToPath: PathManager类型转换成功");

                    // 将PathItem数组转换为BasePathItem列表
                    List<BasePathItem> basePathItems = pathItems.Cast<BasePathItem>().ToList();

                    if (basePathItems.Count < 2)
                    {
                        ModuleLogManager.LogSubmarineError("SubmarineManager.AddSubmarineToPath: 转换后的有效路径点不足2个!");
                        return;
                    }

                    ModuleLogManager.LogSubmarine($"SubmarineManager.AddSubmarineToPath: 转换BasePathItem列表完成，数量: {basePathItems.Count}");

                    // 根据PathKitSettings的autoUpdateDirection设置决定是否使用方向精灵
                    if (pathKitSettings != null && pathKitSettings.autoUpdateDirection)
                    {
                        realManager.AddToPathWithDirection(
                            gameObject,
                            basePathItems,
                            true, // 使用方向精灵
                            PathMoveType.Loop, // 循环移动
                            onReachPoint: (index) => {
                                ModuleLogManager.LogSubmarine($"SubmarineManager: 到达路径点 {index}");
                                OnReachPathPoint(index);
                            },
                            onComplete: () => {
                                ModuleLogManager.LogSubmarine("SubmarineManager: 路径移动完成");
                                OnPathComplete();
                            }
                        );
                    }
                    else
                    {
                        realManager.AddToPath(
                            gameObject,
                            basePathItems,
                            PathMoveType.Loop,
                            onReachPoint: (index) => {
                                ModuleLogManager.LogSubmarine($"SubmarineManager: 到达路径点 {index}");
                                OnReachPathPoint(index);
                            },
                            onComplete: () => {
                                ModuleLogManager.LogSubmarine("SubmarineManager: 路径移动完成");
                                OnPathComplete();
                            }
                        );
                    }

                    ModuleLogManager.LogSubmarine("SubmarineManager.AddSubmarineToPath: 路径添加调用成功");

                    // 应用路径设置
                    pathManager.UpdatePathSettings(gameObject, pathKitSettings);

                    // 最终验证
                    bool finalCheck = pathManager.HasPath(gameObject);
                    ModuleLogManager.LogSubmarine($"SubmarineManager.AddSubmarineToPath: 最终验证，HasPath结果: {finalCheck}");

                    // 🔧 设置标志，防止重复添加
                    if (finalCheck)
                    {
                        isAddedToPath = true;
                        ModuleLogManager.LogSubmarine("SubmarineManager.AddSubmarineToPath: 设置isAddedToPath = true，防止重复添加");
                    }
                }
                else
                {
                    ModuleLogManager.LogSubmarineError($"SubmarineManager.AddSubmarineToPath: PathManager类型转换失败，实际类型: {pathManager?.GetType().Name ?? "null"}");
                }
            }
            catch (System.Exception ex)
            {
                ModuleLogManager.LogSubmarineError($"SubmarineManager.AddSubmarineToPath: 添加潜艇到路径时发生错误: {ex.Message}");
                ModuleLogManager.LogSubmarineError($"SubmarineManager.AddSubmarineToPath: 异常堆栈: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 到达路径点时的处理
        /// </summary>
        private void OnReachPathPoint(int index)
        {
            ModuleLogManager.LogSubmarine($"SubmarineManager.OnReachPathPoint: 到达路径点 {index}");

            try
            {
                // 验证索引有效性
                if (index < 0 || index >= pathItems.Length)
                {
                    ModuleLogManager.LogSubmarineWarning($"SubmarineManager.OnReachPathPoint: 路径点索引 {index} 超出范围 [0, {pathItems.Length - 1}]");
                    return;
                }

                // 更新当前站点索引
                currentStationIndex = index;
                ModuleLogManager.LogSubmarine($"SubmarineManager.OnReachPathPoint: 更新currentStationIndex为 {currentStationIndex}");

                // 获取站点索引并发送事件
                var pathItem = pathItems[index];
                if (pathItem != null)
                {
                    int stationIndex = pathItem.stationIndex;
                    if (stationIndex >= 0)
                    {
                        // 发送到达站点事件
                        TypeEventSystem.Global.Send(new SubmarineStationReachedEvent { StationIndex = stationIndex });
                        ModuleLogManager.LogSubmarine($"SubmarineManager.OnReachPathPoint: 发送SubmarineStationReachedEvent，站点索引: {stationIndex}");
                    }
                    else
                    {
                        ModuleLogManager.LogSubmarineWarning($"SubmarineManager.OnReachPathPoint: 路径点 {index} 的站点索引无效: {stationIndex}");
                    }

                    // 🔧 更新潜艇方向（根据路径移动方向）
                    if (pathKitSettings != null && pathKitSettings.autoUpdateDirection && index + 1 < pathItems.Length)
                    {
                        // 计算到下一个点的方向
                        Vector2 currentPos = pathItem.Position;
                        Vector2 nextPos = pathItems[index + 1].Position;
                        Vector2 direction = (nextPos - currentPos).normalized;

                        // 根据方向确定SubmarineDirection
                        SubmarineDirection submarineDirection = DetermineSubmarineDirection(direction);
                        UpdateSubmarineDirection(submarineDirection);

                        ModuleLogManager.LogSubmarine($"SubmarineManager.OnReachPathPoint: 更新潜艇方向为 {submarineDirection}");
                    }
                }
                else
                {
                    ModuleLogManager.LogSubmarineError($"SubmarineManager.OnReachPathPoint: 路径点 {index} 为空");
                }
            }
            catch (System.Exception ex)
            {
                ModuleLogManager.LogSubmarineError($"SubmarineManager.OnReachPathPoint: 处理路径点到达事件时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 路径完成时的处理
        /// </summary>
        private void OnPathComplete()
        {
            ModuleLogManager.LogSubmarine("SubmarineManager.OnPathComplete: 路径移动完成");

            try
            {
                // 设置移动状态为false
                isMoving = false;

                // 切换到到达状态
                if (submarineFSM != null)
                {
                    submarineFSM.ChangeState(SubmarineStateType.Arrived);
                    ModuleLogManager.LogSubmarine("SubmarineManager.OnPathComplete: 潜艇状态已切换到Arrived");
                }
                else
                {
                    ModuleLogManager.LogSubmarineWarning("SubmarineManager.OnPathComplete: submarineFSM为空，无法切换状态");
                }

                // 注意：不在这里发送SubmarineMoveCompletedEvent，因为OnMovementCompleted()会统一处理
                ModuleLogManager.LogSubmarine("SubmarineManager.OnPathComplete: 路径完成处理完毕，事件将由OnMovementCompleted统一发送");
            }
            catch (System.Exception ex)
            {
                ModuleLogManager.LogSubmarineError($"SubmarineManager.OnPathComplete: 处理路径完成事件时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据移动方向确定潜艇方向
        /// </summary>
        private SubmarineDirection DetermineSubmarineDirection(Vector2 direction)
        {
            // 根据方向向量确定对角线方向
            if (direction.x > 0 && direction.y > 0)
            {
                return SubmarineDirection.RightUp;
            }
            else if (direction.x > 0 && direction.y < 0)
            {
                return SubmarineDirection.RightDown;
            }
            else if (direction.x < 0 && direction.y > 0)
            {
                return SubmarineDirection.LeftUp;
            }
            else if (direction.x < 0 && direction.y < 0)
            {
                return SubmarineDirection.LeftDown;
            }
            else
            {
                // 默认方向
                return SubmarineDirection.RightDown;
            }
        }
        #endregion

        #region 公共接口
        /// <summary>
        /// 静态方法：移动潜艇指定步数（封装单例判空逻辑）
        /// </summary>
        /// <param name="steps">移动步数</param>
        /// <param name="onComplete">移动完成回调</param>
        public static void MoveSubmarineStatic(int steps, System.Action onComplete = null)
        {
            if (Instance != null)
            {
                Instance.MoveSubmarine(steps, onComplete);
            }
            else
            {
                ModuleLogManager.LogSubmarineError("SubmarineManager.MoveSubmarineStatic: SubmarineManager单例未找到，无法执行移动");
                onComplete?.Invoke(); // 确保回调被调用，避免状态不一致
            }
        }

        /// <summary>
        /// 移动潜艇指定步数（主要接口）
        /// </summary>
        /// <param name="steps">移动步数</param>
        /// <param name="onComplete">移动完成回调</param>
        public void MoveSubmarine(int steps, System.Action onComplete = null)
        {
            // 获取调用堆栈信息用于调试
            var stackTrace = new System.Diagnostics.StackTrace(1, true);
            var callerMethod = stackTrace.GetFrame(0)?.GetMethod();
            var callerClass = callerMethod?.DeclaringType?.Name ?? "Unknown";
            var callerMethodName = callerMethod?.Name ?? "Unknown";

            ModuleLogManager.LogSubmarine($"SubmarineManager.MoveSubmarine: 收到移动请求，步数: {steps}，调用者: {callerClass}.{callerMethodName}()");
            ModuleLogManager.LogSubmarine($"SubmarineManager.MoveSubmarine: 当前SubmarineManager.currentStationIndex = {currentStationIndex}");

            // 验证移动参数
            if (steps <= 0)
            {
                ModuleLogManager.LogSubmarineWarning($"SubmarineManager.MoveSubmarine: 无效的移动步数: {steps}，调用者: {callerClass}.{callerMethodName}()");
                onComplete?.Invoke(); // 即使失败也调用回调，确保状态正确切换
                return;
            }

            // 检查是否正在移动 - 关键的防重复调用机制
            if (isMoving)
            {
                ModuleLogManager.LogSubmarineWarning($"SubmarineManager.MoveSubmarine: 潜艇正在移动中，忽略新的移动请求。当前调用者: {callerClass}.{callerMethodName}()");
                onComplete?.Invoke(); // 调用回调，保证状态一致性
                return;
            }

            // 验证必要组件
            if (pathItems == null || pathItems.Length == 0)
            {
                ModuleLogManager.LogSubmarineError($"SubmarineManager.MoveSubmarine: 路径点未初始化，无法移动，调用者: {callerClass}.{callerMethodName}()");
                onComplete?.Invoke();
                return;
            }

            if (submarineRectTransform == null)
            {
                ModuleLogManager.LogSubmarineError($"SubmarineManager.MoveSubmarine: 潜艇RectTransform组件缺失，调用者: {callerClass}.{callerMethodName}()");
                onComplete?.Invoke();
                return;
            }

            ModuleLogManager.LogSubmarine($"SubmarineManager.MoveSubmarine: 开始移动潜艇，步数: {steps}，当前位置: {currentStationIndex}，调用者: {callerClass}.{callerMethodName}()");

            // 🔧 重要修复：检查PathController状态同步问题
            if (pathController != null)
            {
                ModuleLogManager.LogSubmarineWarning($"SubmarineManager.MoveSubmarine: 注意！PathController内部状态可能与currentStationIndex({currentStationIndex})不同步，这可能导致路径差异");
                ModuleLogManager.LogSubmarineWarning($"SubmarineManager.MoveSubmarine: 建议检查PathController的内部位置状态");
            }

            // 设置移动状态 - 立即设置以防止后续重复调用
            isMoving = true;

            // 切换到移动状态
            if (submarineFSM != null)
            {
                submarineFSM.ChangeState(SubmarineStateType.Moving);
                ModuleLogManager.LogSubmarine("SubmarineManager.MoveSubmarine: 潜艇FSM状态已切换到Moving");
            }
            else
            {
                ModuleLogManager.LogSubmarineWarning("SubmarineManager.MoveSubmarine: submarineFSM为空，无法切换状态");
            }

            // 高亮路径（可选功能，不影响核心移动逻辑）
            try
            {
                HighlightPath(currentStationIndex, steps);
            }
            catch (System.Exception ex)
            {
                ModuleLogManager.LogSubmarineWarning($"SubmarineManager.MoveSubmarine: 路径高亮失败: {ex.Message}");
                // 继续执行移动，不因为高亮失败而中断
            }

            // 创建安全的完成回调，确保状态正确更新
            System.Action safeOnComplete = () => {
                ModuleLogManager.LogSubmarine($"SubmarineManager.MoveSubmarine: 移动完成回调被调用，原始调用者: {callerClass}.{callerMethodName}()");
                OnMovementCompleted(steps, onComplete);
            };

            // 执行移动
            try
            {
                // 🔧 重要决定：由于PathController状态同步问题无法解决，强制使用DOTween确保一致性
                ModuleLogManager.LogSubmarineWarning("SubmarineManager.MoveSubmarine: 由于PathController状态不同步问题，强制使用DOTween确保移动路径一致性");

                ModuleLogManager.LogSubmarine($"SubmarineManager.MoveSubmarine: 使用PathController移动（调用者: {callerClass}.{callerMethodName}）");
                    MoveWithPathController(steps, safeOnComplete);
            }
            catch (System.Exception ex)
            {
                ModuleLogManager.LogSubmarineError($"SubmarineManager.MoveSubmarine: 移动执行失败: {ex.Message}，调用者: {callerClass}.{callerMethodName}()");
                // 发生异常时确保状态正确恢复
                safeOnComplete();
            }
        }

        /// <summary>
        /// 重置潜艇位置
        /// </summary>
        public void ResetSubmarinePosition()
        {
            if (pathItems != null && pathItems.Length > 0 && submarineRectTransform != null)
            {
                currentStationIndex = 0;
                submarineRectTransform.anchoredPosition = pathItems[0].Position;
                UpdateSubmarineDirection(SubmarineDirection.RightDown);

                ModuleLogManager.LogSubmarine("SubmarineManager.ResetSubmarinePosition: 潜艇位置已重置");
            }
        }

        /// <summary>
        /// 获取当前站点索引
        /// </summary>
        public int GetCurrentStationIndex()
        {
            return currentStationIndex;
        }

        /// <summary>
        /// 检查潜艇是否正在移动
        /// </summary>
        public bool IsMoving()
        {
            return isMoving;
        }

        /// <summary>
        /// 获取路径点数量
        /// </summary>
        public int GetPathItemCount()
        {
            return pathItems?.Length ?? 0;
        }

        /// <summary>
        /// 启动路径移动（基于PathManager）
        /// </summary>
        public void StartPathMovement()
        {
            ModuleLogManager.LogSubmarine("SubmarineManager.StartPathMovement: 启动路径移动");

            try
            {
                var pathManager = PathKitCore.Interface.GetSystem<IPathManager>();
                if (pathManager != null && pathManager.HasPath(gameObject))
                {
                    pathManager.ResumePath(gameObject);
                    isMoving = true;

                    // 切换到移动状态
                    if (submarineFSM != null)
                    {
                        submarineFSM.ChangeState(SubmarineStateType.Moving);
                    }

                    ModuleLogManager.LogSubmarine("SubmarineManager.StartPathMovement: 路径移动已启动");
                }
                else
                {
                    ModuleLogManager.LogSubmarineError("SubmarineManager.StartPathMovement: PathManager为空或潜艇未添加到路径中");
                }
            }
            catch (System.Exception ex)
            {
                ModuleLogManager.LogSubmarineError($"SubmarineManager.StartPathMovement: 启动路径移动失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 暂停路径移动（基于PathManager）
        /// </summary>
        public void PausePathMovement()
        {
            ModuleLogManager.LogSubmarine("SubmarineManager.PausePathMovement: 暂停路径移动");

            try
            {
                var pathManager = PathKitCore.Interface.GetSystem<IPathManager>();
                if (pathManager != null && pathManager.HasPath(gameObject))
                {
                    pathManager.PausePath(gameObject);
                    isMoving = false;

                    // 切换到等待状态
                    if (submarineFSM != null)
                    {
                        submarineFSM.ChangeState(SubmarineStateType.Arrived);
                    }

                    ModuleLogManager.LogSubmarine("SubmarineManager.PausePathMovement: 路径移动已暂停");
                }
                else
                {
                    ModuleLogManager.LogSubmarineError("SubmarineManager.PausePathMovement: PathManager为空或潜艇未添加到路径中");
                }
            }
            catch (System.Exception ex)
            {
                ModuleLogManager.LogSubmarineError($"SubmarineManager.PausePathMovement: 暂停路径移动失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 停止路径移动（基于PathManager）
        /// </summary>
        public void StopPathMovement()
        {
            ModuleLogManager.LogSubmarine("SubmarineManager.StopPathMovement: 停止路径移动");

            try
            {
                var pathManager = PathKitCore.Interface.GetSystem<IPathManager>();
                if (pathManager != null && pathManager.HasPath(gameObject))
                {
                    pathManager.StopPath(gameObject);
                    isMoving = false;

                    // 切换到空闲状态
                    if (submarineFSM != null)
                    {
                        submarineFSM.ChangeState(SubmarineStateType.Idle);
                    }

                    ModuleLogManager.LogSubmarine("SubmarineManager.StopPathMovement: 路径移动已停止");
                }
                else
                {
                    ModuleLogManager.LogSubmarineError("SubmarineManager.StopPathMovement: PathManager为空或潜艇未添加到路径中");
                }
            }
            catch (System.Exception ex)
            {
                ModuleLogManager.LogSubmarineError($"SubmarineManager.StopPathMovement: 停止路径移动失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查潜艇是否已添加到路径中
        /// </summary>
        public bool IsInPath()
        {
            try
            {
                var pathManager = PathKitCore.Interface.GetSystem<IPathManager>();
                return pathManager?.HasPath(gameObject) ?? false;
            }
            catch (System.Exception ex)
            {
                ModuleLogManager.LogSubmarineError($"SubmarineManager.IsInPath: 检查路径状态失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查本地路径添加状态标志
        /// </summary>
        public bool IsLocallyAddedToPath()
        {
            return isAddedToPath;
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 使用PathController移动
        /// </summary>
        private void MoveWithPathController(int steps, System.Action onComplete)
        {
            ModuleLogManager.LogSubmarine("SubmarineManager.MoveWithPathController: 使用PathController移动");

            // 🔧 关键修复：强制同步PathController状态与currentStationIndex
            ModuleLogManager.LogSubmarineWarning($"SubmarineManager.MoveWithPathController: 检测到状态不同步问题");
            ModuleLogManager.LogSubmarineWarning($"SubmarineManager.MoveWithPathController: SubmarineManager.currentStationIndex = {currentStationIndex}");
            ModuleLogManager.LogSubmarineWarning($"SubmarineManager.MoveWithPathController: PathController可能从不同的内部位置开始移动");

            // 🔧 新增：使用新的SetCurrentPosition方法同步状态
            try
            {
                ModuleLogManager.LogSubmarine("SubmarineManager.MoveWithPathController: 尝试同步PathController位置");
                pathController.LogCurrentState(); // 记录当前状态
                pathController.SetCurrentPosition(currentStationIndex); // 同步位置
                ModuleLogManager.LogSubmarine("SubmarineManager.MoveWithPathController: PathController位置同步完成");
            }
            catch (System.Exception ex)
            {
                ModuleLogManager.LogSubmarineError($"SubmarineManager.MoveWithPathController: PathController位置同步失败: {ex.Message}");
                ModuleLogManager.LogSubmarineWarning("SubmarineManager.MoveWithPathController: 继续使用不同步的状态移动");
            }

            pathController.MoveSteps(steps, () => {
                onComplete?.Invoke();
            });
        }

        /// <summary>
        /// 使用DOTween移动（备用方案）
        /// </summary>
        private void MoveWithDOTween(int steps, System.Action onComplete)
        {
            ModuleLogManager.LogSubmarine("SubmarineManager.MoveWithDOTween: 使用DOTween移动（统一方案）");

            if (pathItems == null || pathItems.Length == 0)
            {
                ModuleLogManager.LogSubmarineError("SubmarineManager.MoveWithDOTween: 路径点为空");
                onComplete?.Invoke();
                return;
            }

            // 🔧 增强：详细记录移动路径信息
            ModuleLogManager.LogSubmarine($"SubmarineManager.MoveWithDOTween: 开始计算移动路径");
            ModuleLogManager.LogSubmarine($"SubmarineManager.MoveWithDOTween: 起始位置 - currentStationIndex: {currentStationIndex}");
            ModuleLogManager.LogSubmarine($"SubmarineManager.MoveWithDOTween: 移动步数: {steps}");
            ModuleLogManager.LogSubmarine($"SubmarineManager.MoveWithDOTween: 路径点总数: {pathItems.Length}");

            // 创建移动路径
            List<Vector3> movePath = new List<Vector3>();

            for (int i = 1; i <= steps; i++)
            {
                int targetIndex = (currentStationIndex + i) % pathItems.Length;
                Vector2 targetPos = pathItems[targetIndex].Position;
                movePath.Add(new Vector3(targetPos.x, targetPos.y, 0));

                // 🔧 增强：记录每个路径点
                ModuleLogManager.LogSubmarine($"SubmarineManager.MoveWithDOTween: 步骤 {i} -> 路径点索引 {targetIndex}, 位置: ({targetPos.x:F1}, {targetPos.y:F1})");
            }

            if (movePath.Count > 0)
            {
                // 计算最终目标位置
                int finalTargetIndex = (currentStationIndex + steps) % pathItems.Length;
                ModuleLogManager.LogSubmarine($"SubmarineManager.MoveWithDOTween: 最终目标位置 - 路径点索引: {finalTargetIndex}");

                // 🔧 统一使用PathKitSettings配置数据
                float moveDuration = pathKitSettings?.defaultMoveDuration ?? 2.0f;
                DG.Tweening.Ease moveEase = pathKitSettings?.defaultMoveEase ?? DG.Tweening.Ease.InOutQuad;
                float totalDuration = moveDuration * steps;

                ModuleLogManager.LogSubmarine($"SubmarineManager.MoveWithDOTween: 使用PathKitSettings配置 - 持续时间: {totalDuration}s, 缓动: {moveEase}");

                submarineRectTransform.DOPath(movePath.ToArray(), totalDuration, DG.Tweening.PathType.Linear)
                    .SetEase(moveEase)
                    .OnComplete(() => {
                        ModuleLogManager.LogSubmarine($"SubmarineManager.MoveWithDOTween: DOTween移动完成，到达路径点索引: {finalTargetIndex}");
                        onComplete?.Invoke();
                    });
            }
            else
            {
                ModuleLogManager.LogSubmarineWarning("SubmarineManager.MoveWithDOTween: 移动路径为空，直接调用完成回调");
                onComplete?.Invoke();
            }
        }

        /// <summary>
        /// 移动完成处理
        /// </summary>
        private void OnMovementCompleted(int steps, System.Action onComplete)
        {
            // 更新当前站点索引
            currentStationIndex = (currentStationIndex + steps) % pathItems.Length;

            // 设置移动状态为false
            isMoving = false;

            // 切换到到达状态
            if (submarineFSM != null)
            {
                submarineFSM.ChangeState(SubmarineStateType.Arrived);
            }

            // 发送移动完成事件（保留关键事件供其他模块监听）
            //TypeEventSystem.Global.Send(new SubmarineMoveCompletedEvent());

            ModuleLogManager.LogSubmarine($"SubmarineManager.OnMovementCompleted: 移动完成，当前站点: {currentStationIndex}");

            // 执行回调
            onComplete?.Invoke();
        }

        /// <summary>
        /// 高亮路径
        /// </summary>
        private void HighlightPath(int startIndex, int steps)
        {
            if (pathItems == null || configService == null) return;

            var config = configService.GetConfig<ScriptableObjects.SubmarineConfig>();
            if (config != null)
            {
                PathItem.HighlightPathToTarget(
                    pathItems.ToList(),
                    startIndex,
                    steps,
                    config
                );
            }
        }

        /// <summary>
        /// 更新潜艇方向
        /// </summary>
        private void UpdateSubmarineDirection(SubmarineDirection direction)
        {
            // 🔧 检查PathKitSettings的自动更新方向设置
            if (pathKitSettings != null && !pathKitSettings.autoUpdateDirection)
            {
                ModuleLogManager.LogSubmarine("SubmarineManager.UpdateSubmarineDirection: PathKitSettings禁用了自动更新方向，跳过方向更新");
                return;
            }

            if (submarineImage != null && directionSprites != null && directionSprites.Length > 0)
            {
                int directionIndex = (int)direction;
                if (directionIndex >= 0 && directionIndex < directionSprites.Length)
                {
                    var sprite = directionSprites[directionIndex];
                    if (sprite != null)
                    {
                        submarineImage.sprite = sprite;
                        ModuleLogManager.LogSubmarine($"SubmarineManager.UpdateSubmarineDirection: 更新潜艇方向为 {direction} (索引: {directionIndex})");
                    }
                    else
                    {
                        ModuleLogManager.LogSubmarineWarning($"SubmarineManager.UpdateSubmarineDirection: 方向 {direction} 的精灵为空，无法更新");
                    }
                }
                else
                {
                    ModuleLogManager.LogSubmarineWarning($"SubmarineManager.UpdateSubmarineDirection: 无效的方向索引 {directionIndex}，精灵数组长度: {directionSprites.Length}");
                }
            }
            else
            {
                // 🔧 增强：详细的错误信息
                string errorDetails = "";
                if (submarineImage == null) errorDetails += "submarineImage为空; ";
                if (directionSprites == null) errorDetails += "directionSprites为空; ";
                else if (directionSprites.Length == 0) errorDetails += "directionSprites长度为0; ";

                ModuleLogManager.LogSubmarineWarning($"SubmarineManager.UpdateSubmarineDirection: 无法更新方向 - {errorDetails}");
            }
        }

        /// <summary>
        /// 验证所有字段赋值状态
        /// </summary>
        private void ValidateAllFields()
        {
            ModuleLogManager.LogSubmarine("SubmarineManager.ValidateAllFields: 开始验证所有字段赋值状态");

            bool hasErrors = false;
            System.Text.StringBuilder validationReport = new System.Text.StringBuilder();
            validationReport.AppendLine("=== SubmarineManager 字段验证报告 ===");
            validationReport.AppendLine("注意：所有组件现在都要求在Inspector中手动配置，不再自动查找或创建");

            // 🔧 验证关键组件字段
            validationReport.AppendLine("\n【关键组件】（必须在Inspector中配置）");
            ValidateField("submarineRectTransform", submarineRectTransform, true, validationReport, ref hasErrors);
            ValidateField("submarineImage", submarineImage, true, validationReport, ref hasErrors);
            ValidateField("submarineFSM", submarineFSM, false, validationReport, ref hasErrors); // 可选

            // 🔧 验证配置相关字段
            validationReport.AppendLine("\n【配置数据】（必须在Inspector中配置）");
            ValidateField("pathKitSettings", pathKitSettings, true, validationReport, ref hasErrors);
            ValidateField("configService", configService, true, validationReport, ref hasErrors);

            // 🔧 验证路径相关字段
            validationReport.AppendLine("\n【路径数据】（必须在Inspector中配置）");
            validationReport.AppendLine("  注意：只使用Inspector中配置的pathItems，忽略PathKitSettings.pathPoints");
            ValidateArrayField("pathItems", pathItems, 2, validationReport, ref hasErrors); // 至少需要2个路径点
            ValidateField("pathController", pathController, false, validationReport, ref hasErrors); // 运行时创建

            // 🔧 验证精灵数据
            validationReport.AppendLine("\n【精灵数据】（从PathKitSettings自动获取）");
            ValidateArrayField("directionSprites", directionSprites, 4, validationReport, ref hasErrors); // 需要4个方向精灵

            // 检查精灵的具体内容
            if (directionSprites != null && directionSprites.Length >= 4)
            {
                for (int i = 0; i < 4; i++)
                {
                    SubmarineDirection dir = (SubmarineDirection)i;
                    bool spriteValid = directionSprites[i] != null;
                    validationReport.AppendLine($"  directionSprites[{i}] ({dir}): {(spriteValid ? "✓" : "✗")}");
                    if (!spriteValid)
                    {
                        ModuleLogManager.LogSubmarineWarning($"SubmarineManager.ValidateAllFields: 方向精灵 {dir} 为空，请检查PathKitSettings配置");
                    }
                }
            }

            // 🔧 验证状态字段
            validationReport.AppendLine("\n【状态数据】");
            validationReport.AppendLine($"  isMoving: {isMoving} (当前状态)");
            validationReport.AppendLine($"  currentStationIndex: {currentStationIndex} (当前位置)");

            // 🔧 验证PathKitSettings配置
            if (pathKitSettings != null)
            {
                validationReport.AppendLine("\n【PathKitSettings配置】");
                validationReport.AppendLine($"  defaultMoveDuration: {pathKitSettings.defaultMoveDuration}s");
                validationReport.AppendLine($"  defaultMoveEase: {pathKitSettings.defaultMoveEase}");
                validationReport.AppendLine($"  autoUpdateDirection: {pathKitSettings.autoUpdateDirection}");
                validationReport.AppendLine($"  directionType: {pathKitSettings.directionType}");
            }

            // 🔧 输出验证结果
            validationReport.AppendLine($"\n【验证结果】");
            if (hasErrors)
            {
                validationReport.AppendLine($"验证状态: ❌ 发现问题 - 请在Inspector中手动配置缺失的组件");
            }
            else
            {
                validationReport.AppendLine($"验证状态: ✅ 全部正常 - 所有必要组件已正确配置");
            }

            string report = validationReport.ToString();
            if (hasErrors)
            {
                ModuleLogManager.LogSubmarineError($"SubmarineManager.ValidateAllFields: 字段验证发现问题\n{report}");
            }
            else
            {
                ModuleLogManager.LogSubmarine($"SubmarineManager.ValidateAllFields: 所有字段验证通过\n{report}");
            }
        }

        /// <summary>
        /// 验证单个字段
        /// </summary>
        private void ValidateField(string fieldName, object fieldValue, bool isRequired, System.Text.StringBuilder report, ref bool hasErrors)
        {
            bool isValid = fieldValue != null;
            string status = isValid ? "✓" : "✗";
            string requirement = isRequired ? "必需" : "可选";

            report.AppendLine($"  {fieldName}: {status} ({requirement})");

            if (!isValid && isRequired)
            {
                hasErrors = true;
                ModuleLogManager.LogSubmarineError($"SubmarineManager.ValidateAllFields: 必需字段 {fieldName} 未赋值");
            }
        }

        /// <summary>
        /// 验证数组字段
        /// </summary>
        private void ValidateArrayField(string fieldName, System.Array fieldArray, int minLength, System.Text.StringBuilder report, ref bool hasErrors)
        {
            bool isValid = fieldArray != null && fieldArray.Length >= minLength;
            string status = isValid ? "✓" : "✗";
            int actualLength = fieldArray?.Length ?? 0;

            report.AppendLine($"  {fieldName}: {status} (长度: {actualLength}, 最小需要: {minLength})");

            if (!isValid)
            {
                hasErrors = true;
                ModuleLogManager.LogSubmarineError($"SubmarineManager.ValidateAllFields: 数组字段 {fieldName} 无效 - 实际长度: {actualLength}, 最小需要: {minLength}");
            }
        }
        #endregion

        #region QFramework接口
        public IArchitecture GetArchitecture()
        {
            return QHLCArchitecture.Interface;
        }
        #endregion

        #region Unity生命周期
        private void OnDestroy()
        {
            // 🔧 清理PathManager路径
            try
            {
                var pathManager = PathKitCore.Interface?.GetSystem<IPathManager>();
                if (pathManager != null && pathManager.HasPath(gameObject))
                {
                    ModuleLogManager.LogSubmarine("SubmarineManager.OnDestroy: 从PathManager移除潜艇路径");
                    pathManager.RemoveFromPath(gameObject);
                }
            }
            catch (System.Exception ex)
            {
                ModuleLogManager.LogSubmarineError($"SubmarineManager.OnDestroy: 清理PathManager失败: {ex.Message}");
            }

            // 停止所有移动
            pathController?.StopMovement();
            DOTween.Kill(submarineRectTransform);

            // 🔧 重置路径标志
            isAddedToPath = false;

            // 清理单例引用
            if (_instance == this)
            {
                _instance = null;
            }

            ModuleLogManager.LogSubmarine("SubmarineManager.OnDestroy: 资源清理完成");
        }
        #endregion

        #region 调试方法
        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        public void DebugMoveSubmarine(int steps = 1)
        {
            ModuleLogManager.LogSubmarine($"SubmarineManager.DebugMoveSubmarine: 调试移动，步数: {steps}");
            MoveSubmarine(steps);
        }

        /// <summary>
        /// 重置路径添加状态（调试用）
        /// </summary>
        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        public void DebugResetPathState()
        {
            ModuleLogManager.LogSubmarine("SubmarineManager.DebugResetPathState: 重置路径状态");
            isAddedToPath = false;

            // 从PathManager中移除
            try
            {
                var pathManager = PathKitCore.Interface?.GetSystem<IPathManager>();
                if (pathManager != null && pathManager.HasPath(gameObject))
                {
                    pathManager.RemoveFromPath(gameObject);
                    ModuleLogManager.LogSubmarine("SubmarineManager.DebugResetPathState: 已从PathManager移除");
                }
            }
            catch (System.Exception ex)
            {
                ModuleLogManager.LogSubmarineError($"SubmarineManager.DebugResetPathState: 移除失败: {ex.Message}");
            }
        }
        #endregion

        /// <summary>
        /// 静态方法：重置潜艇位置（封装单例判空逻辑）
        /// </summary>
        public static void ResetSubmarinePositionStatic()
        {
            if (Instance != null)
            {
                Instance.ResetSubmarinePosition();
            }
            else
            {
                ModuleLogManager.LogSubmarineError("SubmarineManager.ResetSubmarinePositionStatic: SubmarineManager单例未找到，无法重置位置");
            }
        }

        /// <summary>
        /// 静态方法：获取当前站点索引（封装单例判空逻辑）
        /// </summary>
        /// <returns>当前站点索引，如果单例未找到则返回0</returns>
        public static int GetCurrentStationIndexStatic()
        {
            if (Instance != null)
            {
                return Instance.GetCurrentStationIndex();
            }
            else
            {
                ModuleLogManager.LogSubmarineError("SubmarineManager.GetCurrentStationIndexStatic: SubmarineManager单例未找到，返回默认值0");
                return 0;
            }
        }

        /// <summary>
        /// 静态方法：检查潜艇是否正在移动（封装单例判空逻辑）
        /// </summary>
        /// <returns>是否正在移动，如果单例未找到则返回false</returns>
        public static bool IsMovingStatic()
        {
            if (Instance != null)
            {
                return Instance.IsMoving();
            }
            else
            {
                ModuleLogManager.LogSubmarineError("SubmarineManager.IsMovingStatic: SubmarineManager单例未找到，返回默认值false");
                return false;
            }
        }
    }
}