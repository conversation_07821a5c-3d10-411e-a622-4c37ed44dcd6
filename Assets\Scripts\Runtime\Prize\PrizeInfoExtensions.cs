using QFramework;
using UnityEngine;
using F8Framework.Core;

namespace QHLC.Prize
{
    /// <summary>
    /// 奖励信息扩展方法
    /// </summary>
    public static class PrizeInfoExtensions
    {
        /// <summary>
        /// 显示奖励信息 - 委托给 PrizeInfoSystem 处理，避免重复逻辑
        /// </summary>
        public static void ShowPrizeInfo(this IController controller, PrizeType type, int amount, Sprite icon = null)
        {
            // 获取奖励系统
            var prizeSystem = controller.GetSystem<PrizeInfoSystem>();

            if (prizeSystem != null)
            {
                // 委托给 PrizeInfoSystem 的专用方法处理
                prizeSystem.ShowCustomPrizeInfo(type, amount, icon);
            }
            else
            {
                LogF8.LogWarning("PrizeInfoExtensions: 无法获取PrizeInfoSystem，无法显示奖励信息");
            }
        }


    }
}
