%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &6937715653431401293
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4258816571280493094}
  - component: {fileID: 2530953651022995378}
  - component: {fileID: 5269011645967634579}
  - component: {fileID: 403704699491581439}
  - component: {fileID: 7479681993289166645}
  - component: {fileID: 6061006177555773846}
  m_Layer: 0
  m_Name: GameManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4258816571280493094
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6937715653431401293}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2530953651022995378
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6937715653431401293}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac06e4460623d2d42a75ae42dec3021d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  gameController: {fileID: 5269011645967634579}
--- !u!114 &5269011645967634579
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6937715653431401293}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ba9ec4f0334345248922ca3cdfad8eed, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  lightView: {fileID: 0}
  wheelView: {fileID: 0}
  submarineView: {fileID: 0}
  gameFSMCoordinator: {fileID: 0}
  wheelFSM: {fileID: 0}
  submarineFSM: {fileID: 0}
  enableDebugger: 0
--- !u!114 &403704699491581439
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6937715653431401293}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3f7412024d5e99545b5bcae7f435cc42, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  gameConfig: {fileID: 11400000, guid: 42446b7c827f96349bc22996c21ad0ac, type: 2}
  wheelConfig: {fileID: 11400000, guid: 153c12b2e42af6e498be0d91177aeb1f, type: 2}
  lightConfig: {fileID: 11400000, guid: 7ad8acc0d310b2546a6bc016c04ba681, type: 2}
  submarineConfig: {fileID: 11400000, guid: 8adb9cca29cf14f4d8073acb5c916be1, type: 2}
  buildingConfig: {fileID: 11400000, guid: cf42a2dffe6c8ce439650a2493890726, type: 2}
--- !u!114 &7479681993289166645
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6937715653431401293}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e46e0f51d1bdcd541af69c3b91c193ef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  coinIcon: {fileID: 21300000, guid: ef1ee2d7b2e00314c817be0fb6973d9a, type: 3}
  lotteryIcon: {fileID: 21300000, guid: 627d0ae112e0ef94bb5f42987a75c644, type: 3}
  specialIcon: {fileID: 21300000, guid: dd7c4e628aebacb4db8a30768d438a1f, type: 3}
  coinBaseAmount: 1
  lotteryBaseAmount: 30
--- !u!114 &6061006177555773846
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6937715653431401293}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3491950da791ec04eac3fcd92ffee964, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  enableWheelLogs: 1
  enableLightLogs: 1
  enableSubmarineLogs: 1
  enableGeneralLogs: 1
  toggleAllLogsKey: 282
  toggleWheelLogsKey: 283
  toggleLightLogsKey: 284
  toggleSubmarineLogsKey: 285
  toggleGeneralLogsKey: 286
